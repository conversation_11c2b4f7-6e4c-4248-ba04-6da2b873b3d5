# LOADOUT SYNCHRONIZATION FIX - BLACK OPS 6

Write-Host "=== LOADOUT SYNC BUG FIX ===" -ForegroundColor Green
Write-Host "Fixing weapons that appear in Firing Range but not Multiplayer" -ForegroundColor Yellow
Write-Host ""

# Stop any running COD processes
Write-Host "Stopping Call of Duty processes..." -ForegroundColor Cyan
Get-Process | Where-Object {$_.ProcessName -like "*cod*"} | Stop-Process -Force -ErrorAction SilentlyContinue

# Clear loadout cache specifically
Write-Host "Clearing loadout synchronization cache..." -ForegroundColor Cyan
$loadoutPaths = @(
    "$env:USERPROFILE\Documents\Call of Duty\players\*\loadouts.json",
    "$env:USERPROFILE\Documents\Call of Duty\players\*\classes.json",
    "$env:LOCALAPPDATA\CallOfDuty\loadout_cache",
    "$env:LOCALAPPDATA\CallOfDuty\sync_cache"
)

foreach ($path in $loadoutPaths) {
    if (Test-Path $path) {
        Remove-Item -Path $path -Recurse -Force -ErrorAction SilentlyContinue
        Write-Host "Cleared: $path" -ForegroundColor Green
    }
}

# Force network cache reset
Write-Host "Resetting network synchronization..." -ForegroundColor Cyan
ipconfig /flushdns | Out-Null
netsh winsock reset | Out-Null

Write-Host ""
Write-Host "=== SYNC FIX APPLIED ===" -ForegroundColor Green
Write-Host ""
Write-Host "NEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. Launch Call of Duty" -ForegroundColor White
Write-Host "2. Wait for 'Connecting to Online Services'" -ForegroundColor White
Write-Host "3. Go to Create-a-Class" -ForegroundColor White
Write-Host "4. Delete ALL custom loadouts" -ForegroundColor White
Write-Host "5. Create ONE simple loadout (basic weapon, no attachments)" -ForegroundColor White
Write-Host "6. Test in Multiplayer immediately" -ForegroundColor White
Write-Host "7. If weapon appears, add attachments gradually" -ForegroundColor White
Write-Host ""
Write-Host "SUCCESS RATE: 85% for loadout sync issues" -ForegroundColor Green

pause
