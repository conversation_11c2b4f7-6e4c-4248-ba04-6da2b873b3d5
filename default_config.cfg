// Call of Duty Black Ops 6 - Default Configuration for Weapon Rendering Fix
// Generated automatically to fix invisible weapon bug

// Graphics Settings - Conservative for weapon rendering
seta r_fullscreen "1"
seta r_mode "1920x1080"
seta r_displayRefresh "60"

// Texture Settings - Lower quality to fix rendering issues
seta r_textureQuality "1"
seta r_textureFilterAnisotropic "1"
seta r_shadowQuality "1"

// Ray Tracing - DISABLED (major cause of weapon rendering issues)
seta r_raytracing "0"
seta r_raytracedReflections "0"
seta r_raytracedShadows "0"
seta r_dlss "0"

// DirectX Settings
seta r_d3d12 "0"
seta r_gpuScheduling "0"

// Memory Settings - Conservative VRAM usage
seta r_vramTarget "0.8"
seta r_streamingQuality "1"
seta r_textureStreaming "0"

// Weapon Rendering Specific
seta r_weaponFov "65"
seta r_weaponBob "1"
seta r_drawWeapons "1"
seta r_firstPersonWeapon "1"

// Performance Settings
seta r_vsync "0"
seta r_maxfps "144"

// Audio Settings
seta snd_volume "0.8"
seta snd_musicVolume "0.3"

// Network Settings
seta cl_maxpackets "100"
seta cl_packetdup "1"

// Input Settings
seta in_mouse "1"
seta sensitivity "5.0"

// Console and Debug
seta con_enable "1"
seta developer "0"

// Weapon-specific fixes
seta cg_drawGun "1"
seta cg_weaponCycleDelay "0"
seta cg_weaponBob "1"

// Force weapon model loading
seta r_preloadShaders "1"
seta r_preloadTextures "1"
seta r_loadForRenderer "1"

// End of configuration
