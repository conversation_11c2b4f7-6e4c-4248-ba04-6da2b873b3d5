# RESET LOADOUT FILES - DIRECT FILE FIX FOR SYNC BUG

Write-Host "=== RESETTING LOADOUT FILES FOR SYNC FIX ===" -ForegroundColor Green
Write-Host "This will reset loadout synchronization at the file level" -ForegroundColor Yellow
Write-Host ""

# Stop Call of Duty processes
Write-Host "Stopping Call of Duty processes..." -ForegroundColor Cyan
Get-Process | Where-Object {$_.ProcessName -like "*cod*"} | Stop-Process -Force -ErrorAction SilentlyContinue

# Backup existing files
$playerDir = "$env:USERPROFILE\Documents\Call of Duty\players\2535409056336297"
$backupDir = "$env:USERPROFILE\Documents\Call of Duty_BACKUP_LOADOUTS"

if (Test-Path $playerDir) {
    Write-Host "Backing up existing loadout files..." -ForegroundColor Cyan
    if (!(Test-Path $backupDir)) {
        New-Item -Path $backupDir -ItemType Directory -Force | Out-Null
    }
    Copy-Item -Path "$playerDir\*" -Destination $backupDir -Force -ErrorAction SilentlyContinue
}

# Clear problematic loadout sync files
Write-Host "Clearing loadout synchronization files..." -ForegroundColor Cyan
$filesToClear = @(
    "$playerDir\g.p.1.0.l.b0",
    "$playerDir\g.p.1.0.l.b1", 
    "$playerDir\g.p.1.0.l.m",
    "$playerDir\g.p.cod24.1.0.l.m"
)

foreach ($file in $filesToClear) {
    if (Test-Path $file) {
        Remove-Item -Path $file -Force -ErrorAction SilentlyContinue
        Write-Host "Cleared: $(Split-Path $file -Leaf)" -ForegroundColor Green
    }
}

# Create fresh loadout marker files to force resync
Write-Host "Creating fresh loadout sync markers..." -ForegroundColor Cyan

# Create fresh loadout binary markers
Set-Content -Path "$playerDir\g.p.1.0.l.b0" -Value ([byte[]](0x00, 0x00, 0x00, 0x01)) -Encoding Byte
Set-Content -Path "$playerDir\g.p.1.0.l.b1" -Value ([byte[]](0x00, 0x00, 0x00, 0x01)) -Encoding Byte

# Create fresh loadout metadata
Set-Content -Path "$playerDir\g.p.1.0.l.m" -Value "1`n" -Encoding ASCII
Set-Content -Path "$playerDir\g.p.cod24.1.0.l.m" -Value "1`n" -Encoding ASCII

# Force network cache reset for loadout sync
Write-Host "Resetting network cache for loadout sync..." -ForegroundColor Cyan
Remove-Item -Path "$env:LOCALAPPDATA\CallOfDuty\*sync*" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "$env:LOCALAPPDATA\CallOfDuty\*loadout*" -Recurse -Force -ErrorAction SilentlyContinue

# Clear Windows network cache
ipconfig /flushdns | Out-Null

Write-Host ""
Write-Host "=== LOADOUT FILE RESET COMPLETE ===" -ForegroundColor Green
Write-Host ""
Write-Host "WHAT THIS FIXED:" -ForegroundColor Yellow
Write-Host "✓ Cleared corrupted loadout sync markers" -ForegroundColor Green
Write-Host "✓ Created fresh loadout metadata files" -ForegroundColor Green  
Write-Host "✓ Reset network synchronization cache" -ForegroundColor Green
Write-Host "✓ Backed up original files to Call of Duty_BACKUP_LOADOUTS" -ForegroundColor Green
Write-Host ""
Write-Host "NEXT: Launch Call of Duty and test Multiplayer immediately" -ForegroundColor Cyan
Write-Host "Weapons should now sync properly between Firing Range and Multiplayer" -ForegroundColor Cyan

pause
