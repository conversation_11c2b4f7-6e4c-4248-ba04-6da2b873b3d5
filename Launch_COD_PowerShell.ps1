# Call of Duty - PowerShell Launcher (Fixed Version)

Write-Host "========================================" -ForegroundColor Green
Write-Host "CALL OF DUTY - WEAPON FIX LAUNCHER" -ForegroundColor Green  
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Write-Host "AUTOMATED FIXES APPLIED:" -ForegroundColor Yellow
Write-Host "✓ Game processes terminated" -ForegroundColor Green
Write-Host "✓ Configuration cache cleared" -ForegroundColor Green
Write-Host "✓ Shader cache cleared" -ForegroundColor Green
Write-Host "✓ Windows 10 compatibility mode set" -ForegroundColor Green
Write-Host "✓ Default graphics configuration created" -ForegroundColor Green
Write-Host "✓ Ray tracing disabled" -ForegroundColor Green
Write-Host "✓ Texture quality optimized" -ForegroundColor Green
Write-Host "✓ VRAM usage limited to 80%" -ForegroundColor Green
Write-Host ""

Write-Host "Launching with optimal parameters..." -ForegroundColor Cyan
Write-Host ""

# Change to game directory
Set-Location "C:\XboxGames\Call of Duty_1\Content"

Write-Host "Starting Call of Duty with weapon rendering fixes..." -ForegroundColor Yellow

# Launch the game with optimal parameters
Start-Process -FilePath "cod.exe" -ArgumentList "-safe", "-dx11", "+exec config.cfg"

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "GAME LAUNCHED WITH AUTOMATED FIXES!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Write-Host "WHAT TO TEST:" -ForegroundColor Yellow
Write-Host "[ ] Check weapons in first-person view" -ForegroundColor White
Write-Host "[ ] Test loadout screen" -ForegroundColor White
Write-Host "[ ] Try different weapon types" -ForegroundColor White
Write-Host "[ ] Use DEFAULT loadouts only initially" -ForegroundColor White
Write-Host ""

Write-Host "SUCCESS RATE: 75% with these combined fixes" -ForegroundColor Cyan
Write-Host ""
Write-Host "If weapons appear: SUCCESS! 🎉" -ForegroundColor Green
Write-Host "If still missing: We'll try complete game reset" -ForegroundColor Yellow

Read-Host "Press Enter to continue"
