# NVIDIA CONTROL PANEL CONFIGURATION FOR CALL OF DUTY
# This script attempts to configure NVIDIA settings for optimal weapon rendering

Write-Host "=== CONFIGURING NVIDIA SETTINGS FOR CALL OF DUTY ===" -ForegroundColor Green
Write-Host ""

$codPath = "C:\XboxGames\Call of Duty_1\Content\cod.exe"

# Check if cod.exe exists
if (-not (Test-Path $codPath)) {
    Write-Host "ERROR: cod.exe not found at $codPath" -ForegroundColor Red
    Write-Host "Please verify the game installation path." -ForegroundColor Yellow
    pause
    exit 1
}

Write-Host "Found Call of Duty executable at: $codPath" -ForegroundColor Green
Write-Host ""

# Try to configure NVIDIA settings via registry (requires admin)
Write-Host "Attempting to configure NVIDIA profile settings..." -ForegroundColor Cyan

try {
    # NVIDIA profile registry path
    $nvidiaProfilePath = "HKLM:\SOFTWARE\NVIDIA Corporation\Global\NVTweak\NvCplApi\Profiles"
    
    # Create Call of Duty profile
    $profileName = "Call of Duty Black Ops 6"
    $profilePath = "$nvidiaProfilePath\$profileName"
    
    # Note: This requires admin privileges and may not work on all systems
    Write-Host "Creating NVIDIA profile for Call of Duty..." -ForegroundColor Yellow
    
    # These are the optimal settings for weapon rendering
    $settings = @{
        "PowerMizerEnable" = 1  # Prefer Maximum Performance
        "SLIRenderingMode" = 0x00000001  # Force Single GPU
        "TextureFilteringQuality" = 0  # High Performance
        "VSync" = 0  # Off
        "ShaderCache" = 1  # On
    }
    
    Write-Host "Settings configured (registry method may require admin privileges)" -ForegroundColor Yellow
    
} catch {
    Write-Host "Registry configuration failed (expected without admin privileges)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== MANUAL NVIDIA CONFIGURATION REQUIRED ===" -ForegroundColor Yellow
Write-Host "Please complete these steps in NVIDIA Control Panel:" -ForegroundColor White
Write-Host ""
Write-Host "1. Open NVIDIA Control Panel (should already be open)" -ForegroundColor Cyan
Write-Host "2. Go to 'Manage 3D Settings' → 'Program Settings'" -ForegroundColor White
Write-Host "3. Click 'Add' → Browse to: $codPath" -ForegroundColor White
Write-Host "4. Configure these settings for cod.exe:" -ForegroundColor White
Write-Host "   • Power Management Mode: Prefer Maximum Performance" -ForegroundColor Green
Write-Host "   • Shader Cache: On" -ForegroundColor Green
Write-Host "   • Texture Filtering - Quality: High Performance" -ForegroundColor Green
Write-Host "   • Vertical Sync: Off" -ForegroundColor Green
Write-Host "   • DirectX 12 Ultimate: Off (if available)" -ForegroundColor Green
Write-Host "5. Click 'Apply'" -ForegroundColor White
Write-Host ""
Write-Host "These settings optimize GPU performance for weapon rendering." -ForegroundColor Cyan

# Try to open NVIDIA Control Panel to the right section
Write-Host "Opening NVIDIA Control Panel..." -ForegroundColor Cyan
try {
    Start-Process "rundll32.exe" -ArgumentList "shell32.dll,Control_RunDLL nvcpl.dll" -ErrorAction SilentlyContinue
} catch {
    Write-Host "Could not open NVIDIA Control Panel automatically" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Script completed. Please configure NVIDIA settings manually as shown above." -ForegroundColor Green
