/*! For license information please see main.js.LICENSE.txt */
(()=>{"use strict";var t,e,r,n={9725:(t,e,r)=>{r.d(e,{B:()=>f});var n=r(6540),o=r(961),i=r(1448),a=r(8500),c=r(3029),s=r(2901),u=function(){function t(){(0,c.A)(this,t),this.events={}}return(0,s.A)(t,[{key:"on",value:function(t,e){this.events[t]||(this.events[t]=[]),this.events[t].push(e)}},{key:"off",value:function(t,e){this.events[t]&&(this.events[t]=this.events[t].filter((function(t){return t!==e})))}},{key:"emit",value:function(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];this.events[t]&&this.events[t].forEach((function(t){return t.apply(void 0,r)}))}}]),t}(),l=r(436),f=new u,h=n.lazy((function(){return Promise.all([r.e(384),r.e(956),r.e(757)]).then(r.bind(r,6105))})),p=n.lazy((function(){return Promise.all([r.e(384),r.e(956),r.e(778)]).then(r.bind(r,4758))})),d=n.lazy((function(){return Promise.all([r.e(384),r.e(956),r.e(133)]).then(r.bind(r,3824))})),v=(0,l.lh)("game_id"),y=function(){return"iw9"===v?n.createElement(h,null):"jup"===v?n.createElement(p,null):"cer"===v||"sat"===v?n.createElement(d,null):n.createElement("div",null)};window.__telescope__=!0,(0,o.render)(n.createElement(i.Kq,{store:a.A},n.createElement(n.Suspense,{fallback:n.createElement("div",null)},n.createElement(y,null))),document.querySelector("#root"))},2509:(t,e,r)=>{r.d(e,{A:()=>l,f:()=>u});var n=r(2284),o=r(2223),i=r(3265),a=r(436),c={language:(0,a.Z0)(),env:(0,i.A)(),authToken:(0,a.c4)(),isPreload:(0,a.Q3)(),lastInputDevice:(0,a.Dt)(),lastInputGamepad:(0,a.cQ)(),is4k:(0,a.AL)(),platform:(0,a.ZG)(),device:(0,a.ZI)(),net:(0,a.oq)((0,a.ZG)()),isCrossConfirm:(0,a.bH)(),isBrowser:"undefined"!==!("undefined"==typeof Telescope_API_LoadComplete||(0,n.A)(Telescope_API_LoadComplete)),isFocused:!0},s=(0,o.Z0)({name:"global",initialState:c,reducers:{setLastInputDevice:function(t,e){t.lastInputDevice=parseInt(e.payload)},setLastInputGamepad:function(t,e){t.lastInputGamepad=parseInt(e.payload)},setIsFocused:function(t,e){t.isFocused=e.payload},setIsBrowser:function(t,e){t.isBrowser=e.payload}}}),u=s.actions;const l=s.reducer},8500:(t,e,r)=>{r.d(e,{A:()=>a});var n=r(2223),o=r(2509),i=r(7578);const a=(0,n.U1)({reducer:{global:o.A,motd:i.Ay},middleware:function(t){return t()},devTools:!1})},7578:(t,e,r)=>{r.d(e,{Ay:()=>_,Gc:()=>w,TC:()=>y});var n=r(2284),o=r(4467),i=r(467),a=r(2223),c=r(2505),s=r.n(c),u=r(9725),l=r(436);function f(){f=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var i=e&&e.prototype instanceof d?e:d,a=Object.create(i.prototype),c=new A(n||[]);return o(a,"_invoke",{value:E(t,r,c)}),a}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=l;var p={};function d(){}function v(){}function y(){}var g={};u(g,a,(function(){return this}));var m=Object.getPrototypeOf,w=m&&m(m(P([])));w&&w!==e&&r.call(w,a)&&(g=w);var _=y.prototype=d.prototype=Object.create(g);function b(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function x(t,e){function i(o,a,c,s){var u=h(t[o],t,a);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==(0,n.A)(f)&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){i("next",t,c,s)}),(function(t){i("throw",t,c,s)})):e.resolve(f).then((function(t){l.value=t,c(l)}),(function(t){return i("throw",t,c,s)}))}s(u.arg)}var a;o(this,"_invoke",{value:function(t,r){function n(){return new e((function(e,n){i(t,r,e,n)}))}return a=a?a.then(n,n):n()}})}function E(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return{value:void 0,done:!0}}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var c=L(a,r);if(c){if(c===p)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var s=h(t,e,r);if("normal"===s.type){if(n=r.done?"completed":"suspendedYield",s.arg===p)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n="completed",r.method="throw",r.arg=s.arg)}}}function L(t,e){var r=e.method,n=t.iterator[r];if(void 0===n)return e.delegate=null,"throw"===r&&t.iterator.return&&(e.method="return",e.arg=void 0,L(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),p;var o=h(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,p;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,p):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function A(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function P(t){if(t){var e=t[a];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:S}}function S(){return{value:void 0,done:!0}}return v.prototype=y,o(_,"constructor",{value:y,configurable:!0}),o(y,"constructor",{value:v,configurable:!0}),v.displayName=u(y,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,u(t,s,"GeneratorFunction")),t.prototype=Object.create(_),t},t.awrap=function(t){return{__await:t}},b(x.prototype),u(x.prototype,c,(function(){return this})),t.AsyncIterator=x,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new x(l(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(_),u(_,s,"Generator"),u(_,a,(function(){return this})),u(_,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=P,A.prototype={constructor:A,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(O),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,p):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),O(r),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;O(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:P(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),p}},t}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach((function(e){(0,o.A)(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}var d=function(t,e){var r="/codp/v1/motd/net/".concat(arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,"/locationGroup/1,2,3,4,5,6,7/locale/default/language/").concat(e);return"dev"===t?"https://telescope-stage.callofduty.com/api/ts-api".concat(r):"shaqa"===t?"https://shaqa-telescope-inst.kube.activision.com".concat(r):"prod"===t?"https://telescope-api.callofduty.com/api/ts-api".concat(r):"/api/ts-api".concat(r)},v=function(t){var e={},r=(0,l.lh)("game_id"),n=(0,l.tI)("platform");if(e["x-game-ids"]=r,window.Telescope_API_GetInstalledDLC)try{var o=JSON.parse(window.Telescope_API_GetInstalledDLC());Array.isArray(o)&&o.length>0&&((0,l.wi)("Found installed DLC: ".concat(o.join(","))),e["x-telescope-dlc"]=o.join(","))}catch(t){}else window.Telescope_API_LoadComplete||(e["x-telescope-dlc"]="cer"===r?["cer"]:"sat"===r?["sp-sat","mp-sat","zm-sat","ob-sat","br-wzsat"]:["mw2","wz2","jup"],n&&(e["x-telescope-platform"]=n));return e},y=(0,a.zD)("motd/fetchMotdMessages",function(){var t=(0,i.A)(f().mark((function t(e,r){var n,o,i,a,c,u,l,h,y,g,m,w,_,b,x,E;return f().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=r.getState().global,o=n.language,i=n.env,a=n.authToken,c=n.net,u=d(i,o,c),l=p({Authorization:"Bearer ".concat(a)},v()),t.prev=3,t.next=6,s().get(u,{headers:l});case 6:return x=t.sent,console.log("Response keys: === ".concat(Object.keys(x))),console.log("Response.data keys: === ".concat(Object.keys(null==x?void 0:x.data))),console.log("Response.data.data keys: === ".concat(Object.keys(null==x||null===(h=x.data)||void 0===h?void 0:h.data))),console.log("Messages length: === ".concat(null==x||null===(y=x.data)||void 0===y||null===(g=y.data)||void 0===g||null===(m=g.messages[1])||void 0===m?void 0:m.length)),(E=null===(w=x.data)||void 0===w||null===(_=w.data)||void 0===_?void 0:_.messages[1]).forEach((function(t){t.messageId=parseInt(t.messageId,10)})),t.abrupt("return",{messages:E,messageSource:null===(b=x.data)||void 0===b?void 0:b.messageSource});case 16:return t.prev=16,t.t0=t.catch(3),console.log(t.t0),t.abrupt("return",null);case 20:case"end":return t.stop()}}),t,null,[[3,16]])})));return function(e,r){return t.apply(this,arguments)}}()),g={isExpanded:!1,messages:[],selectedMessage:0,activeCarousel:!1,isFirstView:!(0,l.UU)("isFirstView")||!1,needFullScreen:!1,arrowPressed:!1,messageSource:""},m=(0,a.Z0)({name:"motd",initialState:g,reducers:{isExpanded:function(t,e){var r,n,o=!(null===(r=t.messages[t.selectedMessage])||void 0===r||null===(n=r.content)||void 0===n||!n.backgroundVideo),i=(0,l.lh)("game_id");if(o){if(t.isExpanded=e.payload,window.Telescope_API_SetDisplayMode&&"cer"!==i){var a=e.payload?1:0;Telescope_API_SetDisplayMode(a)}window.Telescope_API_FadeMusic&&e.payload&&window.Telescope_API_FadeMusic(0,.2),u.B.emit("sleep")}!e.payload&&window.Telescope_API_FadeMusic&&window.Telescope_API_FadeMusic(1,.2)},setSelectedMessage:function(t,e){e.payload>=0&&e.payload<t.messages.length&&((0,l.n)(t.selectedMessage),(0,l.Ek)(e.payload),t.selectedMessage=e.payload)},setActiveCarousel:function(t,e){t.activeCarousel=e.payload},setIsFirstView:function(t,e){t.isFirstView=e.payload},setNeedFullScreen:function(t,e){t.needFullScreen=e.payload,window.Telescope_API_SetDisplayMode&&window.Telescope_API_SetDisplayMode(e.payload?1:0)},setArrowPressed:function(t,e){t.arrowPressed=e.payload}},extraReducers:function(t){t.addCase(y.fulfilled,(function(t,e){t.messages=e.payload.messages,t.messageSource=e.payload.messageSource}))}}),w=m.actions;const _=m.reducer},3265:(t,e,r)=>{r.d(e,{A:()=>n});const n=function(){return window.location.host.startsWith("dev")?"dev":window.location.host.startsWith("local")?"local":window.location.host.startsWith("stage")?"stage":window.location.host.startsWith("shaqa")?"shaqa":window.location.host.startsWith("telescope-cert")?"cert":"prod"}},436:(t,e,r)=>{r.d(e,{Q3:()=>I,n:()=>nt,Iu:()=>st,O7:()=>ut,c4:()=>A,ZI:()=>D,lh:()=>j,D8:()=>M,Z0:()=>L,oq:()=>C,tI:()=>S,ZG:()=>N,UU:()=>J,H5:()=>at,mq:()=>it,AL:()=>G,bH:()=>F,Dt:()=>k,cQ:()=>O,zf:()=>tt,wi:()=>P,iE:()=>ot,Jz:()=>lt,Ln:()=>et,rK:()=>ct,ge:()=>Y,Ek:()=>rt,eA:()=>T});for(var n=r(2284),o=r(467),i=r(5458),a=r(2505),c=r.n(a),s=r(3265),u={},l=0,f=["auth_token","account_type","build_version","changelist_number","device_type","ffotd","game_session_id","game_session_index","language","locale","page_context","program_type","region","title_id","tu","user_session_id","user_id","last_input_device","last_input_gamepad","is_cross_confirm","resolution","ccs","env"];l<f.length;l++)u[f[l]]=null;const h=u;var p=r(3029),d=r(2901);function v(){v=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var i=e&&e.prototype instanceof p?e:p,a=Object.create(i.prototype),c=new A(n||[]);return o(a,"_invoke",{value:E(t,r,c)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=l;var h={};function p(){}function d(){}function y(){}var g={};u(g,a,(function(){return this}));var m=Object.getPrototypeOf,w=m&&m(m(P([])));w&&w!==e&&r.call(w,a)&&(g=w);var _=y.prototype=p.prototype=Object.create(g);function b(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function x(t,e){function i(o,a,c,s){var u=f(t[o],t,a);if("throw"!==u.type){var l=u.arg,h=l.value;return h&&"object"==(0,n.A)(h)&&r.call(h,"__await")?e.resolve(h.__await).then((function(t){i("next",t,c,s)}),(function(t){i("throw",t,c,s)})):e.resolve(h).then((function(t){l.value=t,c(l)}),(function(t){return i("throw",t,c,s)}))}s(u.arg)}var a;o(this,"_invoke",{value:function(t,r){function n(){return new e((function(e,n){i(t,r,e,n)}))}return a=a?a.then(n,n):n()}})}function E(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return{value:void 0,done:!0}}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var c=L(a,r);if(c){if(c===h)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var s=f(t,e,r);if("normal"===s.type){if(n=r.done?"completed":"suspendedYield",s.arg===h)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n="completed",r.method="throw",r.arg=s.arg)}}}function L(t,e){var r=e.method,n=t.iterator[r];if(void 0===n)return e.delegate=null,"throw"===r&&t.iterator.return&&(e.method="return",e.arg=void 0,L(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),h;var o=f(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,h;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,h):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function A(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function P(t){if(t){var e=t[a];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:S}}function S(){return{value:void 0,done:!0}}return d.prototype=y,o(_,"constructor",{value:y,configurable:!0}),o(y,"constructor",{value:d,configurable:!0}),d.displayName=u(y,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===d||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,u(t,s,"GeneratorFunction")),t.prototype=Object.create(_),t},t.awrap=function(t){return{__await:t}},b(x.prototype),u(x.prototype,c,(function(){return this})),t.AsyncIterator=x,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new x(l(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(_),u(_,s,"Generator"),u(_,a,(function(){return this})),u(_,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=P,A.prototype={constructor:A,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(O),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,h):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),O(r),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;O(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:P(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),h}},t}const y=new(function(){function t(){(0,p.A)(this,t),this.actionMap=new Map,this.taskQueue=[]}return(0,d.A)(t,[{key:"actionStart",value:function(t){var e=this.actionMap.get(t);e?e.start=Date.now():this.actionMap.set(t,{start:Date.now()})}},{key:"actionEnd",value:function(t,e){var r=this.actionMap.get(t);if(r){var n=Date.now()-r.start;this.taskQueue.push((0,o.A)(v().mark((function t(){return v().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e(n);case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)})))),this.flushTasks()}}},{key:"flushTasks",value:function(){var t=this;setTimeout((0,o.A)(v().mark((function e(){var r;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0===t.taskQueue.length){e.next=12;break}return r=t.taskQueue.shift(),e.prev=2,e.next=5,r();case 5:e.next=10;break;case 7:e.prev=7,e.t0=e.catch(2),P(e.t0);case 10:e.next=0;break;case 12:case"end":return e.stop()}}),e,null,[[2,7]])}))),0)}}]),t}());function g(){g=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var i=e&&e.prototype instanceof p?e:p,a=Object.create(i.prototype),c=new A(n||[]);return o(a,"_invoke",{value:E(t,r,c)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=l;var h={};function p(){}function d(){}function v(){}var y={};u(y,a,(function(){return this}));var m=Object.getPrototypeOf,w=m&&m(m(P([])));w&&w!==e&&r.call(w,a)&&(y=w);var _=v.prototype=p.prototype=Object.create(y);function b(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function x(t,e){function i(o,a,c,s){var u=f(t[o],t,a);if("throw"!==u.type){var l=u.arg,h=l.value;return h&&"object"==(0,n.A)(h)&&r.call(h,"__await")?e.resolve(h.__await).then((function(t){i("next",t,c,s)}),(function(t){i("throw",t,c,s)})):e.resolve(h).then((function(t){l.value=t,c(l)}),(function(t){return i("throw",t,c,s)}))}s(u.arg)}var a;o(this,"_invoke",{value:function(t,r){function n(){return new e((function(e,n){i(t,r,e,n)}))}return a=a?a.then(n,n):n()}})}function E(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return{value:void 0,done:!0}}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var c=L(a,r);if(c){if(c===h)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var s=f(t,e,r);if("normal"===s.type){if(n=r.done?"completed":"suspendedYield",s.arg===h)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n="completed",r.method="throw",r.arg=s.arg)}}}function L(t,e){var r=e.method,n=t.iterator[r];if(void 0===n)return e.delegate=null,"throw"===r&&t.iterator.return&&(e.method="return",e.arg=void 0,L(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),h;var o=f(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,h;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,h):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function A(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function P(t){if(t){var e=t[a];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:S}}function S(){return{value:void 0,done:!0}}return d.prototype=v,o(_,"constructor",{value:v,configurable:!0}),o(v,"constructor",{value:d,configurable:!0}),d.displayName=u(v,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===d||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,u(t,s,"GeneratorFunction")),t.prototype=Object.create(_),t},t.awrap=function(t){return{__await:t}},b(x.prototype),u(x.prototype,c,(function(){return this})),t.AsyncIterator=x,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new x(l(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(_),u(_,s,"Generator"),u(_,a,(function(){return this})),u(_,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=P,A.prototype={constructor:A,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(O),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,h):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),O(r),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;O(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:P(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),h}},t}var m={},w=500,_=1e3,b=3,x=1e3,E=5e3,L=function(){return j("language")||"english"},k=function(){var t=j("last_input_device",!0);return t?parseInt(t):1},O=function(){var t=j("last_input_gamepad",!0);return t?parseInt(t):0},A=function(){var t=j("auth_token",!0);if(!t){var e=window.location.search;if(e){var r=e.match(/auth_token=(\w+)/);r&&(t=r[1])}}return t},P=function(t){window.Telescope_API_Print?"string"!=typeof t&&(t=t?t.toString():""+t):window.console&&console.log(t)},S=function(t){t.startsWith("#")&&(t=encodeURIComponent(t)),t=t.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");var e=new RegExp("[\\?&]"+t+"=([^&#]*)").exec(location.search);return null===e?"":decodeURIComponent(e[1].replace(/\+/g," "))},j=function(t,e){if(!e){var r=h[t];if(r)return r}return S(t)?r=S(t):(window.Telescope_API_GetEnvVar?r=h[t]=Telescope_API_GetEnvVar(t):P("Could not load env var '".concat(t,"'")),r)},T=function(t){"undefined"!=typeof Telescope_API_LoadComplete?Telescope_API_LoadComplete(t):P("Telescope_API_LoadComplete not defined")},I=function(){var t=j("page_context",!0);return"preload"==t||"motd"==t||"motd_unknown"==t},G=function(){var t=j("resolution");return 1===t||"1"===t},N=function(){var t=j("program_type");return t=t?{0:"unknown",1:"PC",2:"PS4",3:"PS5",4:"XBOXONE",5:"XBOXSX"}[t]:"unknown"},F=function(){return 0!=parseInt(j("is_cross_confirm"))},D=function(){var t=j("device_type");return t=t?{0:"unknown",2:"PS4",3:"PS4PRO",6:"XBONES",7:"XBONEX",8:"XBSS",9:"XBSX"}[t]:"unknown"},C=function(t){var e=j("account_type"),r={2:3,7:2,8:4,11:"PC"===t?7:1,15:8};return console.log(" THE PLATFORM IS ".concat(t," ============ the accountType is ").concat(e," and this is getNet: ").concat(r[e])),r[e]};function M(){var t=j("default_volume",!0);return"number"!=typeof t||isNaN(t)?1:t}var B={},q=0,V=[],z=null;function R(){var t=[];for(var e in z={})t=[].concat((0,i.A)(z[e]),(0,i.A)(t));return t.forEach((function(t){B[t]||(B[t]=function(t,e){var r;return(r=B[t])||(r=S(t))||window.Telescope_API_GetDvar&&(r=Telescope_API_GetDvar(t)),r}(t))})),B}var X=[],W=Date.now(),Z=(window.location.pathname||"").match(/^\/ts\/\w+\/code\/([^\/]+)\/([^\/]+)\/.*/)||[],Q=Z[1],U=Z[2],Y=function(t,e){"string"!=typeof e&&(e=JSON.stringify(e)||"");for(var r=e,n=0;n<50;n++){var o=t+"_"+n,i=r.substring(0,w);if(r=r.substring(w),H(o,i),i.length<w)break}},J=function(t){for(var e,r=0;r<50;r++){var n=$(t+"_"+r);if(!n)break;if(e=(e||"")+n,n.length<w)break}return e},H=function(t,e){window.Telescope_API_SetState?Telescope_API_SetState(t,e):m[t]=e},$=function(t){return window.Telescope_API_GetState?Telescope_API_GetState(t):m[t]};function K(){var t=[];try{return(t=JSON.parse(J("received_motd_ids")))instanceof Array||(t=[]),t}catch(e){return P(e),t}}var tt=[],et=function(t){t.tsPage="motd",st(t)},rt=function(t){console.log("========== START MOTD VIEW "+t);var e=tt[t];e&&(e.startView=Date.now(),e.type=0)},nt=function(t,e){console.log("========== END MOTD VIEW "+t);var r=tt[t];if(r){var n=Date.now(),o=n-(r.startView||n);r.cumulative=(r.cumulative||0)+o}},ot=function(t){var e=tt[t];e&&(e.clicked=!0)};function it(t){y.actionStart("play_video_msg_index_".concat(t))}function at(t,e){var r=function(){var r=(0,o.A)(g().mark((function r(n){var o;return g().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return o={from_featured:!1,views:[{message_id:e,message_view_duration:n,interacted:!0,message_info_index:t,type:1}]},r.next=3,lt("dlog_event_crm_motd_view",1,o,0);case 3:case"end":return r.stop()}}),r)})));return function(t){return r.apply(this,arguments)}}();y.actionEnd("play_video_msg_index_".concat(t),r)}var ct=function(){var t=(0,o.A)(g().mark((function t(e,r){var n,o,a,c,s,u;return g().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=[],tt.forEach((function(t,e){var r={message_id:t.messageId,message_view_duration:t.cumulative||0,interacted:t.clicked||!1,message_info_index:e+1,type:0};n.push(r)})),o=n,"number"!=typeof e){t.next=11;break}if(a=o[e]){t.next=7;break}return t.abrupt("return");case 7:a.message_info_index=e+1,!r&&a.message_view_duration<1e3?o=[]:(a.message_view_duration<1e3&&(a.message_view_duration=0),o=[a]),t.next=12;break;case 11:void 0===e&&(c=K(),s=[],0!==c.length?(s=o.filter((function(t){return!c.includes(t.message_id)})),o=s,Y("received_motd_ids",[].concat((0,i.A)(c),(0,i.A)(s.map((function(t){return t.message_id})))))):Y("received_motd_ids",o.map((function(t){return t.message_id}))));case 12:if(0!==o.length){t.next=14;break}return t.abrupt("return");case 14:return u={from_featured:!1,views:o.map((function(t){return{message_id:t.message_id,message_view_duration:t.message_view_duration,interacted:t.interacted,message_info_index:t.message_info_index,type:t.type}}))},t.next=17,lt("dlog_event_crm_motd_view",1,u,0);case 17:"number"==typeof e&&tt[e]&&(tt[e].cumulative=0,tt[e].startView=1/0);case 18:case"end":return t.stop()}}),t)})));return function(e,r){return t.apply(this,arguments)}}();function st(t){if(t){setTimeout(ut,2e3);var e=Date.now();t.pageStartTime=W,t.timePageOffset=e-W,t.sessionStartTime=W,t.timeSessionOffset=e-W,t.tsBranch=Q,t.tsVersion=U,X.push(t)}}function ut(){for(var t=[];X.length;)t.push(X.shift()),5==t.length&&(lt("tsui_interactions",1,{interactions:t},0),t=[]);t.length&&lt("tsui_interactions",1,{interactions:t},0)}function lt(t,e,r,n){return ft.apply(this,arguments)}function ft(){return ft=(0,o.A)(g().mark((function t(e,r,n,o){var i,a,u,l,f,p,d,v,y,m=arguments;return g().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(i=m.length>4&&void 0!==m[4]?m[4]:0,(a=j("auth_token"))&&"0"!==a){t.next=5;break}return setTimeout((function(){lt(e,r,n,o,0)}),_),t.abrupt("return");case 5:if(!o){for(u in h)h[u]="env"===u?(0,s.A)():j(u);n.env=h,delete n.env.auth_token}return n.dvars||(n.dvars=R()),l="local"===(0,s.A)()||"dev"===(0,s.A)()||"stage"===(0,s.A)()?"https://stage-telescope-inst.kube.activision.com":"shaqa"===(0,s.A)()?"https://shaqa-telescope-inst.kube.activision.com":"https://telescope-api.callofduty.com/api/ts-inst-gke",f="".concat(l,"/codp/v1/type/").concat(e,"/version/").concat(r),p={},d=JSON.stringify(n),a&&(p.Authorization="Bearer "+a),p["Content-type"]="application/json",p["Access-Control-Allow-Origin"]="*",v=function(t,e){0==(q-=1)&&ht()},q+=1,t.prev=15,t.next=18,Promise.race([c().post(f,n,{headers:p}),new Promise((function(t,e){return setTimeout((function(){return e("dlog request timeout!")}),x)}))]);case 18:y=t.sent,v(!1,y),P("Sent instrumentation ".concat(e,": ").concat(d)),t.next=28;break;case 23:t.prev=23,t.t0=t.catch(15),console.error(t.t0),"Request failed with status code 403"===t.t0.message&&i<b&&setTimeout((function(){lt(e,r,n,o,i+1)}),E),v(!0,t.t0);case 28:(window.location.host||"").startsWith("dev")&&"debug_dump"!=e&&lt("debug_dump",e+"-"+r,n,o);case 29:case"end":return t.stop()}}),t,null,[[15,23]])}))),ft.apply(this,arguments)}var ht=function(){V.forEach((function(t){return t()}))}}},o={};function i(t){var e=o[t];if(void 0!==e)return e.exports;var r=o[t]={exports:{}};return n[t](r,r.exports,i),r.exports}i.m=n,t=[],i.O=(e,r,n,o)=>{if(!r){var a=1/0;for(l=0;l<t.length;l++){for(var[r,n,o]=t[l],c=!0,s=0;s<r.length;s++)(!1&o||a>=o)&&Object.keys(i.O).every((t=>i.O[t](r[s])))?r.splice(s--,1):(c=!1,o<a&&(a=o));if(c){t.splice(l--,1);var u=n();void 0!==u&&(e=u)}}return e}o=o||0;for(var l=t.length;l>0&&t[l-1][2]>o;l--)t[l]=t[l-1];t[l]=[r,n,o]},i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},i.d=(t,e)=>{for(var r in e)i.o(e,r)&&!i.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},i.f={},i.e=t=>Promise.all(Object.keys(i.f).reduce(((e,r)=>(i.f[r](t,e),e)),[])),i.u=t=>t+".js",i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),e={},r="@telescope/monorepo:",i.l=(t,n,o,a)=>{if(e[t])e[t].push(n);else{var c,s;if(void 0!==o)for(var u=document.getElementsByTagName("script"),l=0;l<u.length;l++){var f=u[l];if(f.getAttribute("src")==t||f.getAttribute("data-webpack")==r+o){c=f;break}}c||(s=!0,(c=document.createElement("script")).charset="utf-8",c.timeout=120,i.nc&&c.setAttribute("nonce",i.nc),c.setAttribute("data-webpack",r+o),c.src=t),e[t]=[n];var h=(r,n)=>{c.onerror=c.onload=null,clearTimeout(p);var o=e[t];if(delete e[t],c.parentNode&&c.parentNode.removeChild(c),o&&o.forEach((t=>t(n))),r)return r(n)},p=setTimeout(h.bind(null,void 0,{type:"timeout",target:c}),12e4);c.onerror=h.bind(null,c.onerror),c.onload=h.bind(null,c.onload),s&&document.head.appendChild(c)}},i.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},(()=>{var t;i.g.importScripts&&(t=i.g.location+"");var e=i.g.document;if(!t&&e&&(e.currentScript&&(t=e.currentScript.src),!t)){var r=e.getElementsByTagName("script");if(r.length)for(var n=r.length-1;n>-1&&(!t||!/^http(s?):/.test(t));)t=r[n--].src}if(!t)throw new Error("Automatic publicPath is not supported in this browser");t=t.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),i.p=t})(),(()=>{var t={792:0};i.f.j=(e,r)=>{var n=i.o(t,e)?t[e]:void 0;if(0!==n)if(n)r.push(n[2]);else{var o=new Promise(((r,o)=>n=t[e]=[r,o]));r.push(n[2]=o);var a=i.p+i.u(e),c=new Error;i.l(a,(r=>{if(i.o(t,e)&&(0!==(n=t[e])&&(t[e]=void 0),n)){var o=r&&("load"===r.type?"missing":r.type),a=r&&r.target&&r.target.src;c.message="Loading chunk "+e+" failed.\n("+o+": "+a+")",c.name="ChunkLoadError",c.type=o,c.request=a,n[1](c)}}),"chunk-"+e,e)}},i.O.j=e=>0===t[e];var e=(e,r)=>{var n,o,[a,c,s]=r,u=0;if(a.some((e=>0!==t[e]))){for(n in c)i.o(c,n)&&(i.m[n]=c[n]);if(s)var l=s(i)}for(e&&e(r);u<a.length;u++)o=a[u],i.o(t,o)&&t[o]&&t[o][0](),t[o]=0;return i.O(l)},r=self.webpackChunk_telescope_monorepo=self.webpackChunk_telescope_monorepo||[];r.forEach(e.bind(null,0)),r.push=e.bind(null,r.push.bind(r))})(),i.nc=void 0;var a=i.O(void 0,[417],(()=>i(9725)));a=i.O(a)})();