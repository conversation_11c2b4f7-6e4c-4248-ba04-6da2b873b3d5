# DIAGNOSE CALL OF DUTY LAUNCH ISSUES

Write-Host "=== CALL OF DUTY LAUNCH DIAGNOSTICS ===" -ForegroundColor Green
Write-Host ""

# Check if game files exist
Write-Host "Checking game files..." -ForegroundColor Cyan
$codExe = "Content\cod.exe"
if (Test-Path $codExe) {
    $fileInfo = Get-Item $codExe
    Write-Host "✓ cod.exe found ($([math]::Round($fileInfo.Length / 1MB, 2)) MB)" -ForegroundColor Green
} else {
    Write-Host "✗ cod.exe NOT FOUND!" -ForegroundColor Red
}

# Check NVIDIA driver
Write-Host ""
Write-Host "Checking NVIDIA driver..." -ForegroundColor Cyan
try {
    $gpu = Get-WmiObject Win32_VideoController | Where-Object {$_.Name -like "*NVIDIA*"}
    if ($gpu) {
        Write-Host "✓ NVIDIA GPU: $($gpu.Name)" -ForegroundColor Green
        Write-Host "✓ Driver Version: $($gpu.DriverVersion)" -ForegroundColor Green
        Write-Host "✓ Driver Date: $($gpu.DriverDate)" -ForegroundColor Green
    } else {
        Write-Host "✗ NVIDIA GPU not detected!" -ForegroundColor Red
    }
} catch {
    Write-Host "! Could not check GPU info" -ForegroundColor Yellow
}

# Check running processes
Write-Host ""
Write-Host "Checking for running Call of Duty processes..." -ForegroundColor Cyan
$codProcesses = Get-Process | Where-Object {$_.ProcessName -like "*cod*" -or $_.ProcessName -like "*Call*"}
if ($codProcesses) {
    Write-Host "⚠ Call of Duty processes already running:" -ForegroundColor Yellow
    foreach ($proc in $codProcesses) {
        Write-Host "  - $($proc.ProcessName) (PID: $($proc.Id))" -ForegroundColor White
    }
    Write-Host "Try closing these processes first." -ForegroundColor Yellow
} else {
    Write-Host "✓ No Call of Duty processes running" -ForegroundColor Green
}

# Check DirectX
Write-Host ""
Write-Host "Checking DirectX..." -ForegroundColor Cyan
if (Test-Path "Content\D3D12\D3D12Core.dll") {
    Write-Host "✓ DirectX 12 files found" -ForegroundColor Green
} else {
    Write-Host "! DirectX 12 files missing" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== LAUNCH ALTERNATIVES ===" -ForegroundColor Yellow
Write-Host "1. Try: Launch_COD_Simple.bat (no parameters)" -ForegroundColor White
Write-Host "2. Try: Launch via Xbox App" -ForegroundColor White
Write-Host "3. Try: Launch via Steam/Battle.net (if applicable)" -ForegroundColor White
Write-Host "4. Try: Run cod.exe directly from Content folder" -ForegroundColor White
Write-Host ""
Write-Host "Most important: Test if WEAPONS RENDER after driver fix!" -ForegroundColor Cyan
