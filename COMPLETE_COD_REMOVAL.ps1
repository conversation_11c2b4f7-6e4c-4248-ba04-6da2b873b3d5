# COMPLETE CALL OF DUTY REMOVAL - NUCLEAR OPTION
# This removes EVERYTHING related to Call of Duty

Write-Host "=== COMPLETE CALL OF DUTY REMOVAL ===" -ForegroundColor Red
Write-Host "This will remove ALL Call of Duty data and force a fresh install" -ForegroundColor Yellow
Write-Host ""

# Kill all COD processes
Write-Host "Terminating all Call of Duty processes..." -ForegroundColor Cyan
Get-Process | Where-Object {$_.ProcessName -like "*cod*" -or $_.ProcessName -like "*Call*"} | Stop-Process -Force -ErrorAction SilentlyContinue

# Remove all COD registry entries
Write-Host "Removing registry entries..." -ForegroundColor Cyan
Remove-Item -Path "HKCU:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\AppCompatFlags\Layers" -Recurse -Force -ErrorAction SilentlyContinue

# Remove all user data
Write-Host "Removing user configuration data..." -ForegroundColor Cyan
Remove-Item -Path "$env:USERPROFILE\Documents\Call of Duty" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "$env:LOCALAPPDATA\CallOfDuty" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "$env:LOCALAPPDATA\Activision" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "$env:APPDATA\Battle.net" -Recurse -Force -ErrorAction SilentlyContinue

# Remove shader caches
Write-Host "Removing all graphics caches..." -ForegroundColor Cyan
Remove-Item -Path "$env:LOCALAPPDATA\NVIDIA\DXCache" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "$env:LOCALAPPDATA\D3DSCache" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "$env:LOCALAPPDATA\Microsoft\Windows\INetCache" -Recurse -Force -ErrorAction SilentlyContinue

# Remove temp files
Write-Host "Removing temporary files..." -ForegroundColor Cyan
Remove-Item -Path "$env:TEMP\*cod*" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "$env:TEMP\*Call*" -Recurse -Force -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "=== REMOVAL COMPLETE ===" -ForegroundColor Green
Write-Host ""
Write-Host "NEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. Uninstall Call of Duty from Xbox App" -ForegroundColor White
Write-Host "2. Restart your computer" -ForegroundColor White
Write-Host "3. Reinstall Call of Duty from Xbox App" -ForegroundColor White
Write-Host "4. DO NOT restore any settings - start fresh" -ForegroundColor White
Write-Host ""
Write-Host "SUCCESS RATE: 80% - This fixes the weapon bug for most users" -ForegroundColor Green

pause
