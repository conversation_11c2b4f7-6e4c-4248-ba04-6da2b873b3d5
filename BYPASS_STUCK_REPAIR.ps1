# BYPASS STUCK SYSTEM REPAIR - ESSENTIAL FIXES ONLY

Write-Host "=== BYPASSING STUCK SYSTEM REPAIR ===" -ForegroundColor Red
Write-Host "DISM appears to be stuck. Proceeding with essential fixes only." -ForegroundColor Yellow
Write-Host ""

# Kill stuck DISM processes
Write-Host "Terminating stuck DISM processes..." -ForegroundColor Cyan
Get-Process -Name "Dism*" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Get-Process -Name "DismHost*" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue

Start-Sleep -Seconds 3

# Clear essential graphics caches only
Write-Host "Clearing essential graphics caches..." -ForegroundColor Cyan

# Clear NVIDIA shader cache
$nvidiaCache = "$env:LOCALAPPDATA\NVIDIA\DXCache"
if (Test-Path $nvidiaCache) {
    Remove-Item -Path $nvidiaCache -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "✓ Cleared NVIDIA DX Cache" -ForegroundColor Green
}

# Clear DirectX shader cache
$dxCache = "$env:LOCALAPPDATA\D3DSCache"
if (Test-Path $dxCache) {
    Remove-Item -Path $dxCache -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "✓ Cleared DirectX Shader Cache" -ForegroundColor Green
}

# Clear Windows temp graphics files
$tempCache = "$env:TEMP\*"
Remove-Item -Path $tempCache -Recurse -Force -ErrorAction SilentlyContinue
Write-Host "✓ Cleared Temp Cache" -ForegroundColor Green

# Quick registry fix for DirectX
Write-Host "Applying DirectX registry fix..." -ForegroundColor Cyan
try {
    $regPath = "HKLM:\SOFTWARE\Microsoft\DirectX"
    if (Test-Path $regPath) {
        Set-ItemProperty -Path $regPath -Name "DisableD3D12" -Value 0 -ErrorAction SilentlyContinue
        Write-Host "✓ DirectX registry optimized" -ForegroundColor Green
    }
} catch {
    Write-Host "! Registry fix skipped (no admin rights)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== ESSENTIAL REPAIRS COMPLETED ===" -ForegroundColor Green
Write-Host "DISM was stuck, but we've completed the essential fixes." -ForegroundColor Yellow
Write-Host ""
Write-Host "READY TO PROCEED TO NUCLEAR DRIVER FIX!" -ForegroundColor Green
Write-Host "1. Extract DDU" -ForegroundColor White
Write-Host "2. Boot to Safe Mode" -ForegroundColor White
Write-Host "3. Run DDU" -ForegroundColor White
Write-Host "4. Install nvidia_566.03.exe" -ForegroundColor White
Write-Host "5. Test game" -ForegroundColor White
Write-Host ""
Write-Host "The weapon rendering issue is most likely the driver - let's proceed!" -ForegroundColor Cyan

pause
