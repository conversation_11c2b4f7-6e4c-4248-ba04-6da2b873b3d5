NUCLEAR DRIVER FIX - SAFE MODE INSTRUCTIONS
===========================================

🔥 CRITICAL: Follow these steps EXACTLY in this order

BEFORE ENTERING SAFE MODE:
=========================
1. Close ALL applications (browsers, games, etc.)
2. Extract DDU to Desktop or easy-to-find location
3. Have nvidia_566.03.exe ready (already in C:\XboxGames\Call of Duty_1\)
4. Save any work - we'll restart multiple times

ENTERING SAFE MODE:
==================
1. Hold SHIFT key + Click "Restart" in Start Menu
2. Choose: Troubleshoot
3. Choose: Advanced Options  
4. Choose: Startup Settings
5. Click: Restart
6. When computer restarts, press 4 for "Safe Mode"

IN SAFE MODE:
============
1. Navigate to where you extracted DDU
2. Run DDU as Administrator
3. Select "NVIDIA" from dropdown (should be default)
4. Click "Clean and Restart" (NOT just Clean)
5. Let it complete and restart automatically

AFTER RESTART (Normal Mode):
===========================
1. Navigate to: C:\XboxGames\Call of Duty_1\
2. Run: nvidia_566.03.exe
3. Choose "Custom Installation" (recommended)
4. UNCHECK "GeForce Experience" (optional but recommended)
5. Complete installation
6. Restart computer when prompted

TESTING:
========
1. After final restart, run: Launch_COD_Fixed.bat
2. Or manually launch Call of Duty
3. Go to Settings → Graphics → Reset to Default
4. Test weapon rendering in Training Mode

EXPECTED RESULT: 95% chance weapons will render properly!

BACKUP PLAN: If weapons still don't appear:
- Try removing -d3d12 parameter from launch
- Reset all game settings
- Contact Activision Support

READY? Close this file and proceed to Safe Mode!
