/*! For license information please see 133.js.LICENSE.txt */
"use strict";(self.webpackChunk_telescope_monorepo=self.webpackChunk_telescope_monorepo||[]).push([[133],{3824:(e,t,n)=>{n.r(t),n.d(t,{default:()=>Ot});var r=n(2284),o=n(467),i=n(6540),a=n(1448),c=n(354),l=n(7578),s=n(2509),u=n(436),d=n(3092),f=n(5147),h=n(3802),p=n(4212);const m=n.p+"31d6cfe0d16ae931b73c.ttf";var g,v,y,b,w=n(8500);const E=(0,h.DU)(g||(g=(0,f.A)(["\n  ","\n\n  ","\n\n  *,\n  *::before,\n  *::after {\n    margin: 0;\n    padding: 0;\n    box-sizing: inherit;\n    transition-property: none !important;\n\n    ","\n  }\n\n  html {\n    box-sizing: border-box;\n    font-size: ",";\n  }\n\n  body {\n    font-family: 'Hitmarker Text';\n    color: black;\n    font-weight: normal;\n  }\n\n"])),"local"===w.A.getState().global.env&&(0,h.AH)(v||(v=(0,f.A)(["\n          @font-face {\n              font-family: 'Hitmarker Text';\n              src: url(",") format('opentype');\n          }\n      "])),p),(0,h.AH)(y||(y=(0,f.A)(["\n      @font-face {\n          font-family: 'Hitmarker Condensed';\n          src: url(",") format('opentype');\n      }\n  "])),m),("PC"===w.A.getState().global.platform||"PS5"===w.A.getState().global.platform||"XBOXSX"===w.A.getState().global.platform)&&(0,h.AH)(b||(b=(0,f.A)(["\n            transition-property: all !important;\n        "]))),w.A.getState().global.is4k?"125%":"62.5%"),x=n.p+"64e6258f409ef9f42c5b.png";var _,A,k=h.Ay.section.attrs({"data-ts":"news-container"})(_||(_=(0,f.A)(["\n    width: 192rem;\n    height: 108rem;\n    padding: 0;\n    position: relative;\n    background: ",";\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    background-color: transparent;\n\n\n    .divider {\n        border: 0.01rem solid rgba(231, 231, 231, 0.12);\n    }\n\n    .carousel-controls {\n        display: flex;\n        position: absolute;\n        left: 26.9rem;\n        bottom: 13rem;\n        align-items: center;\n        height: 4rem;\n        &__button-hint {\n            width: 4rem;\n            height: 4rem;\n        }\n\n        .bar {\n            width: 1.2rem;\n            height: 1.2rem;\n            background: ",";\n            transition: all 0.2s;\n            border-radius: 0.2rem;\n\n            &:not(:first-child) {\n                margin-left: 1rem;\n            }\n\n            &-active {\n                background: ",";\n            }\n            &:hover {\n                background: ",";\n            }\n        }\n    }\n\n\n    .nav-arrow {\n        position: absolute;\n        top: 50%;\n        display: flex;\n        transform: translate(0, -50%);\n        align-items: center;\n        justify-content: center;\n        width: 5rem;\n        height: 5rem;\n        cursor: pointer;\n        border-radius:.4rem;\n        background: rgba(126, 127, 134, 0.30);\n        box-shadow: 0px 0px .09rem .03rem rgba(0, 0, 0, 0.25);\n\n        &:hover {\n            border: .1rem solid #F1F1F1;\n            background: linear-gradient(0deg, rgba(255, 255, 255, 0.00) 0%, rgba(255, 255, 255, 0.10) 86.17%, rgba(255, 255, 255, 0.20) 100%), linear-gradient(90deg, rgba(52, 65, 63, 0.50) 0%, rgba(107, 128, 120, 0.50) 74.67%, rgba(132, 160, 148, 0.50) 100%), linear-gradient(0deg, rgba(66, 82, 79, 0.80) 0%, rgba(146, 161, 157, 0.80) 100%)\n        }\n\n        &.left-arrow {\n            left: 12rem;\n        }\n\n        &.right-arrow {\n            right: 12rem;\n        }\n\n        &-image {\n            width: 2.6rem;\n            height: 2.25rem;\n        }\n    }\n    .button {\n        display: flex;\n        font-size: 2rem;\n        font-weight: normal;\n        text-transform: uppercase;\n        width: 8.3rem;\n        height: 5.8rem;\n        border-radius: 0.2rem;\n        display: flex;\n        align-items: center;\n        cursor: pointer;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n\n        &-long {\n            width: 35rem !important;\n        }\n\n        &-text {\n            font-family: 'Hitmarker Text';\n        }\n\n\n        &-primary {\n            color: white;\n            background: url(",");\n            background-size: cover;\n        }\n\n        &-secondary {\n            color: white;\n            background: linear-gradient(\n                    0deg,\n                    rgba(26, 24, 25, 0.7847514005602241) 0%,\n                    rgba(61, 65, 66, 1) 67%,\n                    rgba(62, 62, 62, 1) 100%\n                ),\n                #282828;\n            border: 0.1rem solid ",";\n        &-neutral {\n            color: white;\n            background: linear-gradient(180deg, rgba(231, 231, 231, 0.9) 0%, rgba(231, 231, 231, 0.6) 100%);\n            border: 0.01rem solid rgba(20, 19, 19, 0.24);\n\n            &:hover {\n                background: ",";\n\n                svg {\n                    fill: ",";\n                }\n            }\n        }\n    }\n"])),(function(e){return e.theme.colors.neutrals.neroGray}),(function(e){return e.theme.colors.motd.secondaryInactive}),(function(e){return e.theme.colors.motd.primary}),(function(e){return e.theme.colors.motd.secondaryHover}),x,(function(e){return e.theme.colors.borders.default}),(function(e){return e.theme.colors.primaryGreen}),(function(e){return e.theme.colors.neutralDark})),L=n(7107),S=n(6847);const O=h.Ay.div(A||(A=(0,f.A)(["\n    width: ","rem;\n    height: ","rem;\n    display: ",";\n    justify-content: center;\n    align-items: center;\n    margin-left: ","rem;\n    margin-right: ","rem;\n"])),(function(e){return e.width}),(function(e){return e.height}),(function(e){return e.isPC?"none":"flex"}),(function(e){return e.marginLeft?e.marginLeft:0}),(function(e){return e.marginRight?e.marginRight:0}));function j(){return j=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},j.apply(this,arguments)}const P=function(e){return i.createElement("svg",j({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 58 36"},e),i.createElement("path",{d:"M4 35c-1.65 0-3-1.35-3-3V12C1 5.93 5.93 1 12 1h34c6.07 0 11 4.93 11 11v20c0 1.65-1.35 3-3 3H4Z",style:{fill:"#2e2e2e"}}),i.createElement("path",{d:"M46 0H12C5.37 0 0 5.37 0 12v20c0 2.21 1.79 4 4 4h50c2.21 0 4-1.79 4-4V12c0-6.63-5.37-12-12-12ZM27.85 28H15V8h4.41v16.15h8.44V28Zm16.4 0H30.34v-3.73h4.79V13.36c-1.71.5-3.59.59-5.38.62V10.6c2.79-.06 4.44-.79 5.59-2.59h4.15v16.27h4.76v3.73Z",style:{fill:"#e7e7e7"}}))};var I,C;function N(){return N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},N.apply(this,arguments)}const T=function(e){return i.createElement("svg",N({viewBox:"0 0 47 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),I||(I=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M30.954 8.583c0-.994-.636-1.47-1.908-1.47h-2.662v2.98h2.861c1.113.04 1.709-.477 1.709-1.51ZM29.444 12.04h-3.06v3.497h3.02c1.351 0 2.066-.557 2.066-1.67 0-1.231-.675-1.827-2.026-1.827Z",fill:"#fff"})),C||(C=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M43.192 0H11.166c-.994 0-1.908.437-2.583 1.152L.834 10.013A3.372 3.372 0 0 0 0 12.238v8.345A3.417 3.417 0 0 0 3.417 24h39.855a3.417 3.417 0 0 0 3.417-3.417V3.457C46.649 1.55 45.099 0 43.192 0ZM12.517 17.722V4.967h2.82v10.41h6.24v2.345h-9.06Zm20.503-.994c-.835.676-1.907.994-3.258.994h-6.2V4.967h6.04c2.703 0 4.054 1.033 4.054 3.139 0 1.192-.596 2.106-1.749 2.702.795.238 1.391.636 1.749 1.192.397.556.596 1.232.596 2.066 0 1.113-.398 1.987-1.232 2.662Z",fill:"#fff"})))};function M(){return M=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},M.apply(this,arguments)}const B=function(e){return i.createElement("svg",M({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 58 36"},e),i.createElement("path",{d:"M4.02 35c-1.65 0-3-1.35-3-3V12c0-6.07 4.93-11 11-11h34c6.07 0 11 4.93 11 11v20c0 1.65-1.35 3-3 3h-50Z",style:{fill:"#2e2e2e"}}),i.createElement("path",{d:"M23.63 14.32c0 1.79-.77 2.53-2.91 2.53h-3.29v-5.09h3.29c2.15 0 2.91.74 2.91 2.56ZM58 12v20c0 2.21-1.79 4-4 4H4c-2.21 0-4-1.79-4-4V12C0 5.37 5.37 0 12 0h34c6.63 0 12 5.37 12 12ZM28.81 28l-4.32-7.79c2.5-.74 3.56-2.62 3.56-5.88 0-4.47-1.94-6.32-6.85-6.32h-8.18v20h4.41v-7.38h2.38l3.97 7.38h5.03Zm16.71-3.73h-4.76V8h-4.15c-1.15 1.79-2.79 2.53-5.59 2.59v3.38c1.79-.03 3.68-.12 5.38-.62v10.91h-4.79v3.73h13.91v-3.73Z",style:{fill:"#e7e7e7"}}))};var R,G;function Z(){return Z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Z.apply(this,arguments)}const F=function(e){return i.createElement("svg",Z({viewBox:"0 0 47 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),R||(R=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M32.093 8.569c0-.992-.635-1.468-1.905-1.468h-2.657v2.975h2.856c1.11.04 1.706-.476 1.706-1.507ZM30.585 12.02H27.53v3.49h3.015c1.389 0 2.063-.555 2.063-1.665 0-1.23-.674-1.825-2.023-1.825ZM17.256 7.14h-3.054v3.57h3.094c.595 0 1.07-.118 1.388-.396.357-.278.516-.754.516-1.428 0-.635-.159-1.11-.516-1.349-.357-.238-.793-.396-1.428-.396Z",fill:"#fff"})),G||(G=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M45.779 9.997 38.043 1.15C37.408.397 36.456 0 35.464 0H3.491A3.496 3.496 0 0 0 0 3.49v17.098A3.411 3.411 0 0 0 3.412 24H43.2a3.411 3.411 0 0 0 3.412-3.412v-8.33c0-.833-.278-1.666-.834-2.261Zm-26.222 7.696c-.159-.397-.278-1.111-.357-2.222-.08-1.15-.317-1.904-.635-2.221-.317-.357-.833-.516-1.587-.516h-2.816v4.998h-2.817V4.998h6.863c1.071 0 1.944.318 2.658.992.714.674 1.071 1.508 1.071 2.539 0 1.587-.674 2.658-2.023 3.213v.04c.436.119.793.357 1.071.634.238.318.437.675.595 1.072.159.396.199 1.07.238 1.943.04 1.15.198 1.944.516 2.34h-2.777v-.078Zm14.598-.992c-.833.674-1.904.992-3.253.992h-6.188V4.959h6.03c2.697 0 4.046 1.031 4.046 3.134 0 1.19-.595 2.102-1.745 2.697.793.238 1.388.635 1.745 1.19.397.556.595 1.23.595 2.063 0 1.11-.397 1.983-1.23 2.658Z",fill:"#fff"})))};var H;function z(){return z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},z.apply(this,arguments)}const V=function(e){return i.createElement("svg",z({viewBox:"0 0 20 20",fill:"inherit",xmlns:"http://www.w3.org/2000/svg"},e),H||(H=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10 20c5.523 0 10-4.477 10-10S15.523 0 10 0 0 4.477 0 10s4.477 10 10 10Zm4.588-16 1.41 1.412-4.58 4.586L16 14.575l-1.412 1.41-4.58-4.575L5.423 16l-1.41-1.412L8.596 10 4 5.41 5.412 4l4.594 4.588L14.588 4Z"})))};var U,q;function X(){return X=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},X.apply(this,arguments)}const D=function(e){return i.createElement("svg",X({viewBox:"0 0 24 24",fill:"#fff",xmlns:"http://www.w3.org/2000/svg"},e),U||(U=i.createElement("path",{d:"m11.819 8.241-1.585 4.506h3.17l-1.54-4.506h-.045Z",fill:"inherit"})),q||(q=i.createElement("path",{d:"M12 0C5.366 0 0 5.366 0 12s5.366 12 12 12 12-5.366 12-12S18.611 0 12 0Zm3.057 17.502-.929-2.74H9.532l-.974 2.74h-2.74l4.665-12.294h2.762l4.597 12.294h-2.785Z",fill:"inherit"})))};function K(){return K=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},K.apply(this,arguments)}const Y=function(e){return i.createElement("svg",K({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 64 64"},e),i.createElement("path",{d:"M32 62C15.46 62 2 48.54 2 32S15.46 2 32 2s30 13.46 30 30-13.46 30-30 30Z",style:{fill:"#231f20",opacity:.2}}),i.createElement("path",{"data-name":"Action:_Circle",d:"M44 32c0 6.63-5.37 12-12 12s-12-5.37-12-12 5.37-12 12-12 12 5.37 12 12Zm19 0c0 17.12-13.88 31-31 31S1 49.12 1 32 14.88 1 32 1s31 13.88 31 31Zm-14 0c0-9.39-7.61-17-17-17s-17 7.61-17 17 7.61 17 17 17 17-7.61 17-17Z",style:{fill:"#fff"}}))};function W(){return W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},W.apply(this,arguments)}const J=function(e){return i.createElement("svg",W({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 64 64"},e),i.createElement("circle",{cx:32,cy:32,r:30,style:{fill:"#231f20",opacity:.2}}),i.createElement("path",{"data-name":"Action:_B",d:"M39.7 37.85c0 2.85-1.35 3.8-5.34 3.8h-5.89V33.9h5.29c4.44 0 5.94 1 5.94 3.95ZM63 32c0 17.12-13.88 31-31 31S1 49.12 1 32 14.88 1 32 1s31 13.88 31 31Zm-15.82 6.45c0-4.45-1.65-7.1-5.74-8.25v-.1c3.29-1.15 4.64-3.5 4.64-7.25 0-6.1-3.49-8.85-11.47-8.85H20.99v34h14.66c7.93 0 11.52-2.95 11.52-9.55ZM38.71 23.9c0-2.7-1.2-3.55-4.84-3.55h-5.39v7.2h4.99c3.94 0 5.24-.9 5.24-3.65Z",style:{fill:"#fff"}}))};var Q;function $(){return $=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},$.apply(this,arguments)}const ee=function(e){return i.createElement("svg",$({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 52.34 44.72"},e),Q||(Q=i.createElement("path",{className:"cls-1",d:"M33.71 15.39V2.42c0-1.95-2.19-3.1-3.79-1.99L1.04 20.37a2.42 2.42 0 0 0 0 3.98l28.87 19.94c1.6 1.11 3.79-.04 3.79-1.99V29.86h18.63V15.39H33.7Z"})))};var te;function ne(){return ne=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ne.apply(this,arguments)}const re=function(e){return i.createElement("svg",ne({viewBox:"0 0 20 20",fill:"#fff",xmlns:"http://www.w3.org/2000/svg"},e),te||(te=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10 20c5.523 0 10-4.477 10-10S15.523 0 10 0 0 4.477 0 10s4.477 10 10 10Zm-4-6V6h8v8H6ZM4 4h12v12H4V4Z"})))};function oe(){return oe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},oe.apply(this,arguments)}const ie=function(e){return i.createElement("svg",oe({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 64 64"},e),i.createElement("circle",{cx:32,cy:32,r:17,style:{fill:"#231f20",opacity:.2}}),i.createElement("path",{"data-name":"Stick_x2C__Right_x2C__Horizontal",d:"m63 32-9.26-7.2c-.39-.3-.93.07-.78.54.67 2.1 1.04 4.34 1.04 6.66s-.36 4.56-1.04 6.66c-.***********.78.54L63 32Zm-53 0c0-2.32.36-4.56 1.04-6.66.15-.47-.39-.84-.78-.54L1 32l9.26 7.2c.39.3.93-.07.78-.54A21.89 21.89 0 0 1 10 32Zm22.93-5.61h-2.96v4.58h2.96c1.93 0 2.62-.66 2.62-2.28s-.69-2.3-2.62-2.3ZM32 13.99c-9.95 0-18.01 8.06-18.01 18.01S22.05 50.01 32 50.01 50.01 41.95 50.01 32 41.95 13.99 32 13.99ZM35.69 41l-3.57-6.64h-2.14V41h-3.97V23h7.36c4.42 0 6.17 1.67 6.17 5.69 0 2.94-.95 4.63-3.2 5.29l3.89 7.01H35.7Z",style:{fill:"#fff"}}))},ae=n.p+"8da94cda784705cab940.png";var ce=function(e){var t=e.type,n=e.is4k,r=e.width,o=e.height,a=e.lastInputGamepad,c=e.fill;switch(t){case"LB_L1":if(3===a||4===a)return i.createElement(P,{width:n?20*r:10*r,height:n?20*o:10*o});if(2===a)return i.createElement(T,{width:n?20*r:10*r,height:n?20*o:10*o});case"RB_R1":if(3===a||4===a)return i.createElement(B,{width:n?20*r:10*r,height:n?20*o:10*o});if(2===a)return i.createElement(F,{width:n?20*r:10*r,height:n?20*o:10*o});case"XBA_PSCROSS":if(3===a||4===a)return i.createElement(V,{width:n?20*r:10*r,height:n?20*o:10*o,fill:c||"#e7e7e7"});if(2===a)return i.createElement(D,{width:n?20*r:10*r,height:n?20*o:10*o,fill:"#e7e7e7"});case"XBB_PSCIRCLE":if(3===a||4===a)return i.createElement(Y,{width:n?20*r:10*r,height:n?20*o:10*o,fill:c||"#e7e7e7"});if(2===a)return i.createElement(J,{width:n?20*r:10*r,height:n?20*o:10*o,fill:c||"#e7e7e7"});case"XBX_PSSQUARE":return i.createElement(re,{width:n?20*r:10*r,height:n?20*o:10*o,fill:c||"#e7e7e7"});case"R_STICK":if(3===a||4===a)return i.createElement(ie,{width:n?20*r:10*r,height:n?20*o:10*o,fill:c||"#e7e7e7"});if(2===a)return i.createElement("img",{src:ae,width:n?20*r:10*r,height:n?20*o:10*o});default:return null}};const le=function(e){var t=e.type,n=e.width,r=void 0===n?3.2:n,o=e.height,c=void 0===o?3.2:o,l=e.marginLeft,s=e.marginRight,u=e.fill,d=e.style,f=(0,a.d4)((function(e){return e.global.lastInputDevice})),h=(0,a.d4)((function(e){return e.global.lastInputGamepad})),p=(0,a.d4)((function(e){return e.global.is4k}));return 1!==f||"rightArrow"!==t&&"leftArrow"!==t?0===f?i.createElement(O,{width:r,height:c,marginLeft:l,marginRight:s,style:d},i.createElement(ce,{type:t,is4k:p,width:r,height:c,lastInputGamepad:h,fill:u})):null:i.createElement(O,{marginLeft:l,marginRight:s},"rightArrow"===t&&i.createElement(ee,{width:p?48:24,height:p?96:48,fill:u||"#9AA39A",style:{transform:"scale(-1)"}}),"leftArrow"===t&&i.createElement(ee,{width:p?48:24,height:p?96:48,fill:u||"#9AA39A"}))};var se,ue,de,fe=n(3265),he=n(3029),pe=n(2901),me=n(4467),ge={},ve=function(e){var t;return!(arguments.length>1&&void 0!==arguments[1]&&arguments[1])&&(t=ge[e])||(t=function(e){e.startsWith("#")&&(e=encodeURIComponent(e)),e=e.replace(/\[/,"\\[").replace(/[\]]/,"\\]");var t=new RegExp("[\\?&]".concat(e,"=([^&#]*)")).exec(location.search);return null===t?"":decodeURIComponent(t[1].replace(/\+/g," "))}(e))?t:window.Telescope_API_GetEnvVar?ge[e]=window.Telescope_API_GetEnvVar(e):void ye("Could not load env var '".concat(e,"'"))},ye=function(e){var t;window.process&&"test"===(null===(t=process.env)||void 0===t?void 0:"production")||(window.Telescope_API_Print?window.Telescope_API_Print(String(e)):console.log(e))};var be="hilite",we="select_confirm",Ee="select_back",xe="global_panel_tab",_e="HiliteTab",Ae="popup",ke={motd:(se={},(0,me.A)(se,be,"uin_codhq_highlight"),(0,me.A)(se,we,"uin_codhq_select_confirm"),(0,me.A)(se,Ee,"uin_codhq_select_back"),(0,me.A)(se,xe,"uin_cer_global_panel_tab"),(0,me.A)(se,_e,"uin_cer_highlight_tab"),(0,me.A)(se,Ae,"uin_cer_popup"),se)},Le={cer:(ue={},(0,me.A)(ue,be,"uin_cer_highlight"),(0,me.A)(ue,we,"uin_cer_select_confirm"),(0,me.A)(ue,Ee,"uin_cer_select_back"),(0,me.A)(ue,xe,"uin_cer_global_panel_tab"),(0,me.A)(ue,_e,"uin_cer_highlight_tab"),(0,me.A)(ue,Ae,"uin_cer_popup"),ue)},Se=(de={},(0,me.A)(de,be,"ui_hilite"),(0,me.A)(de,we,"ui_select_confirm"),(0,me.A)(de,Ee,"ui_select_back"),(0,me.A)(de,xe,"uin_global_panel_tab"),(0,me.A)(de,_e,"ui_mpmenu_tab_play"),(0,me.A)(de,Ae,"ui_generic_pop_up"),de),Oe=new WeakSet,je=function(){function e(){var t,n;(0,he.A)(this,e),function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}(t=this,n=Oe),n.add(t)}return(0,pe.A)(e,[{key:"play",value:function(e){if(window.Telescope_API_PlaySound){var t=function(e,t,n){if(!t.has(e))throw new TypeError("attempted to get private field on non-instance");return n}(this,Oe,Pe).call(this,e);t||!window.location.host.startsWith("local")?window.Telescope_API_PlaySound(t):console.error("Unknown sound: ".concat(e))}}}]),e}();function Pe(e){var t=ke[ve("page_context")],n=Le[ve("game_id")];return(null==t?void 0:t[e])||(null==n?void 0:n[e])||Se[e]}const Ie=new je;var Ce=n(5458);const Ne=n.p+"9a93c9e09833a32dbcf8.png";var Te,Me,Be,Re,Ge,Ze,Fe,He,ze,Ve="2rem",Ue=h.Ay.div.attrs({"data-ts":"news-item"})(Te||(Te=(0,f.A)(["\n    &.item {\n        visibility: hidden;\n        &.active {\n            visibility: visible;\n        }\n    }\n    width: 153.6rem;\n    height: 86.8rem;\n    overflow: hidden;\n    position: relative;\n    display: flex;\n    align-items: center;\n    transition: all 0.2s;\n    border-radius: ",";\n    border: 0.2rem solid transparent;\n    background: linear-gradient(black, black) padding-box,\n        linear-gradient(",", ",") border-box;\n    position: absolute;\n\n    .image {\n        width: 100%;\n        height: 100%;\n        background: ",";\n        background-size: cover;\n        background-position: center center;\n        transition: all 0.2s;\n        border-radius: ",";\n        background-clip: padding-box;\n        position: relative;\n\n        .overlay {\n            position: absolute;\n            width: 100%;\n            height: 100%;\n            border-radius: ",";\n        }\n    }\n\n    .content-overlay {\n        position: absolute;\n        top: 0;\n        right: 0;\n        bottom: 0;\n        left: 0;\n\n        .close-button-wrapper {\n            position: absolute;\n            width: 100%;\n            display: flex;\n            justify-content: flex-end;\n            padding: 2.2rem;\n\n            .close-button-console {\n                display: flex;\n                align-items: center;\n                color: #f1f1f1;\n                font-size: 2rem;\n            }\n\n            .close-button-pc {\n                width: 2.8rem;\n                height: 2.8rem;\n                opacity: 0.5;\n                &:hover {\n                    opacity: 0.9;\n                }\n            }\n        }\n\n        .content-container {\n            width: 100%;\n            height: 100%;\n            display: flex;\n            align-items: flex-end;\n            padding: 8rem;\n\n            ","\n            ","\n\n            ","\n        }\n\n        .content {\n            width: 60.8rem;\n            transition: all 0.2s;\n            ","\n            .message-title {\n                font-family: 'Hitmarker Condensed';\n                font-size: 7.2rem;\n                color: ",";\n                line-height: 1;\n                letter-spacing: ",";\n\n                ","\n\n                ","\n            }\n\n            .message-body {\n                font-size: 2rem;\n                color: ",";\n                margin-top: 0.8rem;\n                line-height: ",";\n                font-weight: normal;\n                letter-spacing: 0.1rem;\n                b,\n                strong {\n                    font-weight: bold;\n                    color: #e7e7e7;\n                }\n                ","\n\n                ","\n            }\n\n            .button-bar {\n                margin-top: 2.4rem;\n                display: flex;\n\n                .button {\n                    padding: 1.6rem 2rem;\n                    font-size: 2rem;\n                    font-weight: normal;\n                    text-transform: uppercase;\n                    width: 41rem;\n                    height: 5rem;\n                    border-radius: 0.4rem;\n                    display: flex;\n                    align-items: center;\n                    cursor: pointer;\n                    justify-content: center;\n                    color: ",";\n                    background: url(",");\n                    background-size: cover;\n\n                    &-focused,\n                    &:hover {\n                        border: 0.1rem solid #f1f1f1;\n                        background: linear-gradient(\n                                0deg,\n                                rgba(255, 255, 255, 0) 0%,\n                                rgba(255, 255, 255, 0.1) 86.17%,\n                                rgba(255, 255, 255, 0.2) 100%\n                            ),\n                            linear-gradient(90deg, rgba(52, 65, 63, 0.5) 0%, rgba(107, 128, 120, 0.5) 74.67%, rgba(132, 160, 148, 0.5) 100%),\n                            linear-gradient(0deg, rgba(66, 82, 79, 0.8) 0%, rgba(146, 161, 157, 0.8) 100%);\n                    }\n                }\n            }\n        }\n    }\n"])),Ve,(function(e){return e.theme.colors.motd.primaryLight}),(function(e){return e.theme.colors.motd.primary}),(function(e){return e.imageUrl?"url(".concat(e.imageUrl,")"):"none"}),Ve,Ve,(function(e){return"center"===e.alignment&&(0,h.AH)(Me||(Me=(0,f.A)(["\n                    justify-content: center;\n                "])))}),(function(e){return"right"===e.alignment&&(0,h.AH)(Be||(Be=(0,f.A)(["\n                    justify-content: flex-end;\n                "])))}),(function(e){return"arabic"===e.language&&(0,h.AH)(Re||(Re=(0,f.A)(["\n                    text-align: right;\n                "])))}),(function(e){return"arabic"===e.language&&(0,h.AH)(Ge||(Ge=(0,f.A)(["\n                    direction: rtl;\n                "])))}),(function(e){return e.theme.colors.motd.primary}),(function(e){return"arabic"===e.language?"0":"0.3rem"}),(function(e){return"small"===e.titleFontSize&&(0,h.AH)(Ze||(Ze=(0,f.A)(["\n                        font-size: 3.6rem;\n                    "])))}),(function(e){return"large"===e.titleFontSize&&(0,h.AH)(Fe||(Fe=(0,f.A)(["\n                        font-size: 7.8rem;\n                    "])))}),(function(e){return e.theme.colors.motd.secondary}),(function(e){return"arabic"===e.language?"130%":"115%"}),(function(e){return"normal"===e.longFontSize&&(0,h.AH)(He||(He=(0,f.A)(["\n                        font-size: 1.6rem;\n                    "])))}),(function(e){return"large"===e.longFontSize&&(0,h.AH)(ze||(ze=(0,f.A)(["\n                        font-size: 2.8rem;\n                    "])))}),(function(e){return e.theme.colors.motd.secondary}),Ne),qe=n(7294);const Xe=n.p+"fe1c733bceb84c525c2a.png";function De(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return Ke(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ke(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){c=!0,i=e},f:function(){try{a||null==n.return||n.return()}finally{if(c)throw i}}}}function Ke(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}const Ye=function(){for(var e=new Set,t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];for(var i=[].concat(n);0!==i.length;){var a=i.shift();if(a)if("string"==typeof a){var c,l=De(a.split(/\s+/));try{for(l.s();!(c=l.n()).done;){var s=c.value;e.add(s)}}catch(e){l.e(e)}finally{l.f()}}else if(Array.isArray(a))for(var u=a.length-1;u>=0;u--)i.unshift(a[u]);else if("object"===(0,r.A)(a))for(var d=0,f=Object.keys(a);d<f.length;d++){var h=f[d];a.hasOwnProperty&&!Object.prototype.hasOwnProperty.call(a,h)||a[h]&&e.add(h)}else e.add(String(a))}return Array.from(e).join(" ")};function We(){return We=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},We.apply(this,arguments)}const Je=function(e){return i.createElement("svg",We({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 56 56"},e),i.createElement("path",{d:"M50.37 0H5.63A5.63 5.63 0 0 0 0 5.63v44.74A5.63 5.63 0 0 0 5.63 56h44.74A5.63 5.63 0 0 0 56 50.37V5.63A5.63 5.63 0 0 0 50.37 0Zm-6.15 39.59-4.63 4.63L28 32.63 16.41 44.22l-4.63-4.63L23.37 28 11.78 16.41l4.63-4.63L28 23.37l11.59-11.59 4.63 4.63L32.63 28l11.59 11.59Z",style:{fill:"#fff"}}))},Qe=function(e){var t,n,r,o,c,s,u,d,f,p,m,g,v=e.message,y=e.currentIndex,b=e.handleClose,w=e.expand,E=e.direct,x=e.selectedMessage,_=(0,a.wA)(),A=(0,a.d4)((function(e){return e.global.isCrossConfirm})),k=(0,a.d4)((function(e){return e.global.isFocused})),L=(0,a.d4)((function(e){return e.global.language})),S=(0,a.d4)((function(e){return e.global.lastInputDevice})),O=((0,a.d4)((function(e){return e.motd.messageSource})),0===S),j=(0,i.useRef)(),P=(0,h.DP)(),I="arabic"===L;return i.createElement(Ue,{imageUrl:null!=v&&null!==(t=v.content)&&void 0!==t&&t.popupImageAdditions&&(null==v||null===(n=v.content)||void 0===n?void 0:n.popupImageAdditions.length)>0?function(e){if(e){if(y!==x)return j.current&&clearInterval(j.current),e.content.popupImage;if(y===x&&k){var t,n,r,o,i=null==e||null===(t=e.content)||void 0===t?void 0:t.popupImageAdditions,a=null==e||null===(n=e.display)||void 0===n?void 0:n.cycleTimeOfImage,c=0;if(0===i.length)return;(o=(0,Ce.A)(i)).unshift(null==e||null===(r=e.content)||void 0===r?void 0:r.popupImage),document.querySelectorAll(".image").length>0&&(document.querySelectorAll(".image")[y].style.backgroundImage="url(".concat(o[0],")")),j.current&&clearInterval(j.current),g=setInterval((function(){c>=o.length&&(c=0),document.querySelectorAll(".image")[y].style.backgroundImage="url(".concat(o[c],")"),c++}),a),j.current=g}}}(v):null==v||null===(r=v.content)||void 0===r?void 0:r.popupImage,alignment:null==v||null===(o=v.display)||void 0===o?void 0:o.motdAlignment,titleFontSize:null==v||null===(c=v.display)||void 0===c?void 0:c.motdTitleFontSize,longFontSize:null==v||null===(s=v.display)||void 0===s?void 0:s.motdLongFontSize,language:L,onClick:function(e){return e.stopPropagation()},className:Ye("item",{active:y===x})},i.createElement("div",{className:"image"},i.createElement("div",{className:"overlay",style:{background:"url(".concat(Xe,")"),backgroundSize:"cover"}})),i.createElement("div",{className:"content-overlay"},i.createElement("div",{className:"close-button-wrapper",onClick:b},O?i.createElement("div",{className:"close-button-console"},i.createElement("div",{className:"close-button-content"},null===qe.A||void 0===qe.A||null===(u=qe.A[L])||void 0===u?void 0:u.close),i.createElement(le,{type:A?"XBB_PSCIRCLE":"XBA_PSCROSS",width:3,height:3,marginLeft:1,fill:"#f1f1f1"})):i.createElement("div",{className:"close-button-pc",onMouseEnter:function(){return Ie.play(be)}},i.createElement("div",{className:"close-button-pc__x"},i.createElement(Je,null)))),i.createElement("div",{className:"content-container"},i.createElement("div",{className:"content"},(null==v||null===(d=v.content)||void 0===d?void 0:d.title)&&i.createElement("div",{className:"message-title",dangerouslySetInnerHTML:{__html:v.content.title}}),(null==v||null===(f=v.content)||void 0===f?void 0:f.contentLong)&&i.createElement("div",{className:"message-body",dangerouslySetInnerHTML:{__html:v.content.contentLong}}),((null==v||null===(p=v.content)||void 0===p?void 0:p.backgroundVideo)||(null==v||null===(m=v.content)||void 0===m?void 0:m.navURL))&&i.createElement("div",{className:"button-bar"},i.createElement("div",{className:O?"button button-focused":"button button-unfocused",onClick:function(){return v.content.backgroundVideo?w():E()},onMouseEnter:function(){O||(Ie.play(be),_(l.Gc.setArrowPressed(!1)))}},i.createElement(le,{type:A?"XBA_PSCROSS":"XBB_PSCIRCLE",width:2.4,height:2.4,marginRight:I?0:.8,marginLeft:I?.8:0,fill:P.colors.motd.secondaryText}),i.createElement("div",{className:"button-text"},function(){var e,t,n=null===qe.A||void 0===qe.A||null===(e=qe.A[L])||void 0===e?void 0:e.select;if(v&&v.content)return v.content.backgroundVideo?null===qe.A||void 0===qe.A||null===(t=qe.A[L])||void 0===t?void 0:t.watchVideo:v.content.navURL&&v.data.navMenu&&v.data.navMenu||n}())))))))};function $e(){$e=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function u(e,t,n,r){var i=t&&t.prototype instanceof h?t:h,a=Object.create(i.prototype),c=new L(r||[]);return o(a,"_invoke",{value:x(e,n,c)}),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var f={};function h(){}function p(){}function m(){}var g={};s(g,a,(function(){return this}));var v=Object.getPrototypeOf,y=v&&v(v(S([])));y&&y!==t&&n.call(y,a)&&(g=y);var b=m.prototype=h.prototype=Object.create(g);function w(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function i(o,a,c,l){var s=d(e[o],e,a);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==(0,r.A)(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){i("next",e,c,l)}),(function(e){i("throw",e,c,l)})):t.resolve(f).then((function(e){u.value=e,c(u)}),(function(e){return i("throw",e,c,l)}))}l(s.arg)}var a;o(this,"_invoke",{value:function(e,n){function r(){return new t((function(t,r){i(e,n,t,r)}))}return a=a?a.then(r,r):r()}})}function x(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return{value:void 0,done:!0}}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var c=_(a,n);if(c){if(c===f)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var l=d(e,t,n);if("normal"===l.type){if(r=n.done?"completed":"suspendedYield",l.arg===f)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(r="completed",n.method="throw",n.arg=l.arg)}}}function _(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,_(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),f;var o=d(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,f;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,f):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,f)}function A(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function k(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function L(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(A,this),this.reset(!0)}function S(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:O}}function O(){return{value:void 0,done:!0}}return p.prototype=m,o(b,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:p,configurable:!0}),p.displayName=s(m,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,m):(e.__proto__=m,s(e,l,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},w(E.prototype),s(E.prototype,c,(function(){return this})),e.AsyncIterator=E,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new E(u(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},w(b),s(b,l,"Generator"),s(b,a,(function(){return this})),s(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=S,L.prototype={constructor:L,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(k),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),f},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),k(n),f}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;k(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:S(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),f}},e}const et=function(e){var t=e.messages,n=(0,a.wA)(),r=(0,a.d4)((function(e){return e.global.lastInputDevice})),c=(0,a.d4)((function(e){return e.global.isCrossConfirm})),s=(0,a.d4)((function(e){return e.motd.selectedMessage})),f="undefined"!=typeof Telescope_API_LoadComplete,h=0===r;(0,i.useEffect)((function(){return"dev"===(0,fe.A)()&&(0,u.Jz)("debug_dump","init",{isTelescope:f},0),function(){(0,u.n)(s)}}),[]);var p=function(e){e.tsPage="motd",(0,u.Iu)(e)},m=function(){var e=(0,o.A)($e().mark((function e(){return $e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("Close MOTD"),Ie.play(Ee),(0,u.n)(s),e.next=5,(0,u.rK)(s,!0);case 5:window.Telescope_API_SetDisplayMode&&Telescope_API_SetDisplayMode(0),Telescope_CB_LoseFocus(),(0,u.H5)(s,t[s].messageId);case 8:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),g=function(){s!==t.length-1&&(console.log("Flip to next page"),Ie.play(we),p({type:"click"}),n(l.Gc.setSelectedMessage(s+1)),(0,u.rK)(s))},v=function(){0!==s&&(console.log("Flip to previous page"),Ie.play(we),p({type:"click"}),n(l.Gc.setSelectedMessage(s-1)),(0,u.rK)(s))},y=function(){n(l.Gc.isExpanded(!0)),(0,u.iE)(s),(0,u.n)(s),(0,u.rK)(s,!0),(0,u.mq)(s)},b=function(){var e=(0,o.A)($e().mark((function e(){var n,r;return $e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return(0,u.O7)(),(0,u.iE)(s),(0,u.n)(s),e.next=5,(0,u.rK)(s,!0);case 5:window.Telescope_API_GotoByUri&&Telescope_API_GotoByUri(null===(n=t[s])||void 0===n||null===(r=n.content)||void 0===r?void 0:r.navURL);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),w=function(){return(0,d.cZ)(t,s)?y():(0,d.Mj)(t,s)?b():void 0};return(0,S.A)(s,v,g,m,w),(0,L.A)(1,{LEFT:v,RIGHT:g,XBA_PSCROSS:c?w:m,XBB_PSCIRCLE:c?m:w}),i.createElement(k,{onWheel:function(e){e.deltaY>0?g():e.deltaY<0&&v()},onClick:function(){return m()},id:"news-container"},t.map((function(e,t){return i.createElement(Qe,{message:e,currentIndex:t,handleClose:m,key:e.messageId,expand:y,direct:b,selectedMessage:s})})),t.length>1&&i.createElement(i.Fragment,null,i.createElement("div",{className:"carousel-controls",onClick:function(e){return e.stopPropagation()}},h&&i.createElement(le,{type:"R_STICK",width:4,height:4,fill:"#f1f1f1"}),t.map((function(e,t){return i.createElement("div",{className:"bar ".concat(s===t&&"bar-active"),key:e.messageId,onMouseEnter:function(){return Ie.play(be)},onClick:function(){n(l.Gc.setSelectedMessage(t)),(0,u.rK)(t),Ie.play(we)}})})))),1===r&&i.createElement("div",{className:"nav-arrows",onClick:function(e){return e.stopPropagation()}},0!==s&&i.createElement("div",{className:"nav-arrow left-arrow",onMouseEnter:function(){return Ie.play(be)},onClick:function(){v()}},i.createElement(le,{type:"leftArrow",width:2.6,height:2.25,fill:"#f1f1f1"})),s!==t.length-1&&i.createElement("div",{className:"nav-arrow right-arrow",onMouseEnter:function(){return Ie.play(be)},onClick:function(){g()}},i.createElement(le,{type:"rightArrow",width:2.6,height:2.25,fill:"#f1f1f1"}))))};var tt,nt,rt,ot,it,at=h.Ay.section.attrs({"data-ts":"expanded-news-container"})(tt||(tt=(0,f.A)(["\n    width: 192rem;\n    height: 108rem;\n    background: black;\n    overflow-x: hidden;\n    overflow-y: hidden;\n\n    .close {\n        width: max-content;\n        height: 5rem;\n        color: white;\n        font-size: 3.6rem;\n        display: flex;\n        align-items: center;\n        text-transform: uppercase;\n        font-size: 2rem;\n\n        &:hover {\n        }\n\n        &-button {\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            width: 5rem;\n            height: 5rem;\n            margin: 0 1rem;\n            padding: 0.4rem;\n            border-radius: 0.4rem;\n            border: 0.1rem solid #afafaf;\n            background: #1c1c1e;\n\n            &:hover {\n                opacity: 1;\n                border: 0.1rem solid #f1f1f1;\n                background: linear-gradient(0deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.1) 86.17%, rgba(255, 255, 255, 0.2) 100%),\n                    linear-gradient(90deg, rgba(52, 65, 63, 0.5) 0%, rgba(107, 128, 120, 0.5) 74.67%, rgba(132, 160, 148, 0.5) 100%),\n                    linear-gradient(0deg, rgba(66, 82, 79, 0.8) 0%, rgba(146, 161, 157, 0.8) 100%);\n            }\n\n            span {\n                opacity: 1;\n            }\n        }\n    }\n"]))),ct=h.Ay.div.attrs({"data-ts":"expanded-news-item"})(nt||(nt=(0,f.A)(["\n    width: 100%;\n    height: 103.2rem;\n    background: #e5e5e5;\n    border-top-left-radius: 1.6rem;\n    border-top-right-radius: 1.6rem;\n    overflow: hidden;\n    background: ",";\n    background-repeat: no-repeat;\n    background-size: cover;\n    background-position: center center;\n    display: flex;\n    flex-direction: column;\n    position: relative;\n    border-top-left-radius: 2rem;\n    border-top-right-radius: 2rem;\n    overflow: none;\n    border: none;\n\n    .expanded-content {\n        height: 100%;\n        color: ",";\n        font-size: 5rem;\n        padding-bottom: 7.2rem;\n        padding-left: 7.8rem;\n        display: flex;\n        flex-direction: column;\n        justify-content: flex-end;\n        max-width: 75.5rem;\n\n        &-title {\n            width: 100%;\n            font-size: 7.2rem;\n            overflow: hidden;\n            white-space: nowrap;\n            margin-bottom: 3.2rem;\n        }\n\n        &-long {\n            font-size: 2.4rem;\n            margin-bottom: 3.2rem;\n            white-space: pre-line;\n        }\n\n        .message-cta {\n            display: flex;\n            align-items: center;\n            width: 33.4rem;\n            background: ",";\n            padding: 0.4rem 2rem;\n            font-size: 2rem;\n            color: ",";\n            text-transform: uppercase;\n            z-index: 4;\n        }\n    }\n\n    .video-control-container {\n        display: flex;\n        z-index: 5;\n\n        &-hidden {\n            display: none;\n        }\n\n        .control {\n            width: 12.8rem;\n            height: 3.2rem;\n            border: 0.1rem solid ",";\n            background: linear-gradient(180deg, rgba(255, 255, 255, 0.14) 0%, rgba(255, 255, 255, 0) 114.81%), rgba(20, 19, 19, 1);\n            border-radius: 0.4rem;\n            transition: all 0.2s;\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            position: relative;\n\n            &:not(:first-child) {\n                margin-left: 0.8rem;\n            }\n\n            &:hover {\n                background: ",";\n                border: 0.1rem solid ",";\n            }\n\n            &-active {\n                background: ",";\n                border: 0.1rem solid ",";\n            }\n        }\n    }\n"])),(function(e){return e.imageUrl?"linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, .68), rgba(0, 0, 0, 0.85)), url(".concat(e.imageUrl,")"):"none"}),(function(e){return e.theme.colors.neutralLight}),(function(e){return e.theme.colors.primaryGreen}),(function(e){return e.theme.colors.neutralDark}),(function(e){return e.theme.colors.elPaso}),(function(e){return e.theme.colors.gradients.active}),(function(e){return e.theme.colors.borders.darkBlue}),(function(e){return e.theme.colors.gradients.active}),(function(e){return e.theme.colors.borders.darkBlue})),lt=(h.Ay.nav.attrs({"data-ts":"expanded-news-nav"})(rt||(rt=(0,f.A)(["\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    z-index: 3;\n\n    .button-control {\n        height: 3.2rem;\n        width: 3.2rem;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n\n        &-left {\n            margin-left: 2.1rem;\n            margin-right: 1.2rem;\n        }\n\n        &-right {\n            margin-right: 2.1rem;\n            margin-left: 1.2rem;\n        }\n    }\n\n    .message-nav {\n        display: flex;\n\n        &-button {\n            width: 44rem;\n            background: rgba(20, 19, 19, 1);\n            margin-right: 1rem;\n            font-size: 3.2rem;\n            color: ",";\n            text-transform: uppercase;\n            padding: 1.6rem;\n            text-align: center;\n            border-bottom: 0.4rem solid ",";\n            transition: all 0.2s;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            white-space: nowrap;\n\n            &:last-of-type {\n                margin-right: 0;\n            }\n\n            &-unfocused {\n                border-bottom: 0.4rem solid transparent;\n                background: rgba(20, 19, 19, 1);\n            }\n        }\n    }\n"])),(function(e){return e.theme.colors.neutralLight}),(function(e){return e.theme.colors.primaryGreen})),n(296)),st=h.Ay.div.attrs({"data-ts":"news-video"})(ot||(ot=(0,f.A)(["\n    position: absolute;\n    width: 100%;\n    height: 100%;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    .video {\n        height: 100%;\n        width: 100%;\n    }\n\n    video {\n        width: 100%;\n        height: 100%;\n        object-fit: cover;\n        &::cue {\n            font-family: 'Hitmarker Normal';\n            text-shadow: 0.3rem 0.3rem black;\n            background: none;\n            background-image: none;\n        }\n    }\n"]))),ut=h.Ay.div(it||(it=(0,f.A)(["\n    position: absolute;\n    width: 100%;\n    height: 100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n\n    div.spinner {\n        position: relative;\n        width: 5.4rem;\n        height: 5.4rem;\n        display: inline-block;\n        margin-left: 50%;\n        margin-right: 50%;\n        padding: 1rem;\n        border-radius: 1rem;\n        z-index: 2;\n    }\n\n    div.spinner div {\n        width: 6%;\n        height: 16%;\n        background: #fff;\n        position: absolute;\n        left: 49%;\n        top: 43%;\n        opacity: 0;\n        border-radius: 5rem;\n        box-shadow: 0 0 0.3rem rgba(0, 0, 0, 1);\n        animation: fade 1s linear infinite;\n    }\n\n    @keyframes fade {\n        from {\n            opacity: 1;\n        }\n        to {\n            opacity: 0.25;\n        }\n    }\n\n    div.spinner div.bar1 {\n        transform: rotate(0deg) translate(0, -130%);\n        animation-delay: 0s;\n    }\n\n    div.spinner div.bar2 {\n        transform: rotate(30deg) translate(0, -130%);\n        animation-delay: -0.9167s;\n    }\n\n    div.spinner div.bar3 {\n        transform: rotate(60deg) translate(0, -130%);\n        animation-delay: -0.833s;\n    }\n    div.spinner div.bar4 {\n        transform: rotate(90deg) translate(0, -130%);\n        animation-delay: -0.7497s;\n    }\n    div.spinner div.bar5 {\n        transform: rotate(120deg) translate(0, -130%);\n        animation-delay: -0.667s;\n    }\n    div.spinner div.bar6 {\n        transform: rotate(150deg) translate(0, -130%);\n        animation-delay: -0.5837s;\n    }\n    div.spinner div.bar7 {\n        transform: rotate(180deg) translate(0, -130%);\n        animation-delay: -0.5s;\n    }\n    div.spinner div.bar8 {\n        transform: rotate(210deg) translate(0, -130%);\n        animation-delay: -0.4167s;\n    }\n    div.spinner div.bar9 {\n        transform: rotate(240deg) translate(0, -130%);\n        animation-delay: -0.333s;\n    }\n    div.spinner div.bar10 {\n        transform: rotate(270deg) translate(0, -130%);\n        animation-delay: -0.2497s;\n    }\n    div.spinner div.bar11 {\n        transform: rotate(300deg) translate(0, -130%);\n        animation-delay: -0.167s;\n    }\n    div.spinner div.bar12 {\n        transform: rotate(330deg) translate(0, -130%);\n        animation-delay: -0.0833s;\n    }\n"])));const dt=function(){return i.createElement(ut,null,i.createElement("div",{className:"spinner"},i.createElement("div",{className:"bar1"}),i.createElement("div",{className:"bar2"}),i.createElement("div",{className:"bar3"}),i.createElement("div",{className:"bar4"}),i.createElement("div",{className:"bar5"}),i.createElement("div",{className:"bar6"}),i.createElement("div",{className:"bar7"}),i.createElement("div",{className:"bar8"}),i.createElement("div",{className:"bar9"}),i.createElement("div",{className:"bar10"}),i.createElement("div",{className:"bar11"}),i.createElement("div",{className:"bar12"})))};var ft;const ht=function(e){var t=e.setOnStartLoading,n=e.src,r=e.videoFocused,o=e.pause,a=e.restart,c=e.setRestart,l=e.setPause,s=e.setVideoMounted,d=e.subtitlePath,f=(0,i.useState)(!1),h=(0,lt.A)(f,2),p=h[0],m=h[1];(0,i.useEffect)((function(){var e=!1;if(r&&n){if((ft=ft||document.createElement("video")).setAttribute("src",n),ft.setAttribute("type","video/mp4"),ft.setAttribute("preload","metadata"),d){var o=document.createElement("track");o.setAttribute("src",d),o.setAttribute("kind","subtitles"),o.setAttribute("default",""),o.mode="showing",ft.appendChild(o)}document.querySelector(".video").appendChild(ft),ft.onloadstart=function(){e||(m(!0),s(!0),t&&t(!1))},ft.oncanplay=function(){e||(m(!1),t&&t(!0))},ft.onwaiting=function(){e||m(!0)},ft.onended=function(){e||l(!0)},ft.onplaying=function(){console.log("PLAYING")},ft.onpause=function(){console.log("PASUED")}}return function(){e=!0,document.querySelector(".video video")&&(d&&(document.querySelector(".video track").src="",document.querySelector(".video track").remove()),document.querySelector(".video video").src="",document.querySelector(".video video").remove()),t&&t(!1)}}),[]);return(0,i.useEffect)((function(){var e;o?ft.pause():(e=(0,u.D8)(),ft.volume=e,ft.play())}),[o]),(0,i.useEffect)((function(){a&&(ft.currentTime=0,c(!1))}),[a]),i.createElement(st,null,p&&i.createElement(dt,null),i.createElement("div",{className:"video"}))};var pt;function mt(){return mt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},mt.apply(this,arguments)}const gt=function(e){return i.createElement("svg",mt({viewBox:"0 0 16 22",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),pt||(pt=i.createElement("path",{d:"M5.667 1.667H1v18.666h4.667V1.667ZM15 1.667h-4.667v18.666H15V1.667Z",stroke:"#E7E7E7",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})))};var vt;function yt(){return yt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},yt.apply(this,arguments)}const bt=function(e){return i.createElement("svg",yt({viewBox:"0 0 24 22",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),vt||(vt=i.createElement("path",{d:"M1.5 2.694v6.222m0 0h6.3m-6.3 0 4.872-4.521a9.495 9.495 0 0 1 5.36-2.637 9.552 9.552 0 0 1 5.89 1.074 9.379 9.379 0 0 1 4.053 4.354 9.227 9.227 0 0 1 .59 5.885 9.315 9.315 0 0 1-3.111 5.054 9.517 9.517 0 0 1-5.562 2.193 9.54 9.54 0 0 1-5.78-1.548 9.351 9.351 0 0 1-3.676-4.668",stroke:"#E7E7E7",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})))};var wt;function Et(){return Et=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Et.apply(this,arguments)}const xt=function(e){return i.createElement("svg",Et({viewBox:"0 0 15 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),wt||(wt=i.createElement("path",{d:"M1 1.833v16.334L13.833 10 1 1.833Z",fill:"#E7E7E7",stroke:"#E7E7E7",strokeWidth:2,strokeLinejoin:"round"})))},_t=function(e){var t,n,r,o,c=e.close,l=(0,a.d4)((function(e){return e.motd.messages})),s=(0,a.d4)((function(e){return e.motd.selectedMessage})),u=(0,a.d4)((function(e){return e.global.is4k})),f=(0,a.d4)((function(e){return e.global.isCrossConfirm})),h=l[s],p=(0,i.useState)(1),m=(0,lt.A)(p,2),g=m[0],v=m[1],y=(0,i.useState)(null),b=(0,lt.A)(y,2),w=(b[0],b[1]),E=(0,i.useState)(!1),x=(0,lt.A)(E,2),_=x[0],A=x[1],k=(0,i.useState)(!1),S=(0,lt.A)(k,2),O=S[0],j=S[1],P=(0,i.useState)(!1),I=(0,lt.A)(P,2),C=I[0],N=I[1],T=(0,i.useState)(!1),M=(0,lt.A)(T,2),B=M[0],R=M[1],G=(0,d.sg)(R,2e3),Z=function(){R(!0),G(!1)},F=function(){H[g].callback()},H=[{type:"restart",icon:i.createElement(bt,{width:u?48:24,height:u?44:22}),callback:function(){j(!0),A(!1)}},{type:"playback",icon:i.createElement(gt,{width:u?32:16,height:u?44:22}),altIcon:i.createElement(xt,{width:u?30:15,height:u?40:20}),callback:function(){return A(!_)},swap:_}];return(0,L.A)(1,{LEFT:function(){Z(),v(0===g?H.length-1:g-1)},RIGHT:function(){Z(),v(g===H.length-1?0:g+1)},XBB_PSCIRCLE:f?c:F,XBA_PSCROSS:f?F:c,LTRIG:function(){},DOWN:function(){},UP:function(){}}),(0,i.useEffect)((function(){window.Telescope_API_FadeMusic&&Telescope_API_FadeMusic(0,.2)}),[]),i.createElement(ct,{imageUrl:null==h||null===(t=h.content)||void 0===t?void 0:t.popupImage},i.createElement("div",{className:"expanded-content"},(null==h||null===(n=h.content)||void 0===n?void 0:n.backgroundVideo)&&i.createElement(ht,{videoFocused:!0,src:null==h||null===(r=h.content)||void 0===r?void 0:r.backgroundVideo,pause:_,restart:O,setRestart:j,setPause:A,setVideoMounted:N,subtitlePath:null==h||null===(o=h.display)||void 0===o?void 0:o.videoSubtitleBookKeeperPath}),C&&i.createElement("div",{className:"video-control-container ".concat(!_&&!B&&"video-control-container-hidden")},H.map((function(e,t){return i.createElement("div",{className:"control ".concat(g===t&&"control-active"),onClick:function(){v(t),e.callback()},key:e.type,onMouseEnter:function(){return w(t)},onMouseLeave:function(){return w(null)}},g===t&&i.createElement(le,{type:f?"XBA_PSCROSS":"XBB_PSCIRCLE",width:2,height:2,marginLeft:.2,fill:"#e7e7e7",style:{position:"absolute",left:"2rem"}}),e.swap&&e.altIcon||e.icon)})))))};function At(){At=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function u(e,t,n,r){var i=t&&t.prototype instanceof h?t:h,a=Object.create(i.prototype),c=new L(r||[]);return o(a,"_invoke",{value:x(e,n,c)}),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var f={};function h(){}function p(){}function m(){}var g={};s(g,a,(function(){return this}));var v=Object.getPrototypeOf,y=v&&v(v(S([])));y&&y!==t&&n.call(y,a)&&(g=y);var b=m.prototype=h.prototype=Object.create(g);function w(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function i(o,a,c,l){var s=d(e[o],e,a);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==(0,r.A)(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){i("next",e,c,l)}),(function(e){i("throw",e,c,l)})):t.resolve(f).then((function(e){u.value=e,c(u)}),(function(e){return i("throw",e,c,l)}))}l(s.arg)}var a;o(this,"_invoke",{value:function(e,n){function r(){return new t((function(t,r){i(e,n,t,r)}))}return a=a?a.then(r,r):r()}})}function x(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return{value:void 0,done:!0}}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var c=_(a,n);if(c){if(c===f)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var l=d(e,t,n);if("normal"===l.type){if(r=n.done?"completed":"suspendedYield",l.arg===f)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(r="completed",n.method="throw",n.arg=l.arg)}}}function _(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,_(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),f;var o=d(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,f;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,f):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,f)}function A(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function k(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function L(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(A,this),this.reset(!0)}function S(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:O}}function O(){return{value:void 0,done:!0}}return p.prototype=m,o(b,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:p,configurable:!0}),p.displayName=s(m,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,m):(e.__proto__=m,s(e,l,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},w(E.prototype),s(E.prototype,c,(function(){return this})),e.AsyncIterator=E,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new E(u(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},w(b),s(b,l,"Generator"),s(b,a,(function(){return this})),s(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=S,L.prototype={constructor:L,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(k),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),f},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),k(n),f}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;k(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:S(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),f}},e}const kt=function(){var e,t=(0,a.wA)(),n=(0,a.d4)((function(e){return e.global.is4k})),r=(0,a.d4)((function(e){return e.global.isCrossConfirm})),c=(0,a.d4)((function(e){return e.motd.selectedMessage})),s=(0,a.d4)((function(e){return e.global.language})),d=(0,a.d4)((function(e){return e.motd.messages})),f=function(){var e=(0,o.A)(At().mark((function e(){var n,r;return At().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=document.querySelector(".video video"),r=document.querySelector(".video track"),Ie.play(Ee),t(l.Gc.isExpanded(!1)),n&&(n.src="",r&&(n.textTracks[0].mode="hidden",r.src="",r.remove())),(0,u.H5)(c,d[c].messageId),(0,u.Ek)(c);case 7:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return i.createElement(at,null,i.createElement("div",{className:"close",onClick:function(){return f()},onMouseEnter:function(){return Ie.play(be)}},i.createElement("div",{className:"close-button left-arrow"},i.createElement(ee,{width:n?48:24,height:n?96:48,fill:"#f1f1f1"}),i.createElement(le,{type:r?"XBB_PSCIRCLE":"XBA_PSCROSS",width:2,height:2,marginLeft:.4,fill:"#e7e7e7"})),null===qe.A||void 0===qe.A||null===(e=qe.A[s])||void 0===e?void 0:e.close),i.createElement(_t,{close:f}))};var Lt=n(9725);function St(){St=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function u(e,t,n,r){var i=t&&t.prototype instanceof h?t:h,a=Object.create(i.prototype),c=new L(r||[]);return o(a,"_invoke",{value:x(e,n,c)}),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var f={};function h(){}function p(){}function m(){}var g={};s(g,a,(function(){return this}));var v=Object.getPrototypeOf,y=v&&v(v(S([])));y&&y!==t&&n.call(y,a)&&(g=y);var b=m.prototype=h.prototype=Object.create(g);function w(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function i(o,a,c,l){var s=d(e[o],e,a);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==(0,r.A)(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){i("next",e,c,l)}),(function(e){i("throw",e,c,l)})):t.resolve(f).then((function(e){u.value=e,c(u)}),(function(e){return i("throw",e,c,l)}))}l(s.arg)}var a;o(this,"_invoke",{value:function(e,n){function r(){return new t((function(t,r){i(e,n,t,r)}))}return a=a?a.then(r,r):r()}})}function x(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return{value:void 0,done:!0}}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var c=_(a,n);if(c){if(c===f)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var l=d(e,t,n);if("normal"===l.type){if(r=n.done?"completed":"suspendedYield",l.arg===f)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(r="completed",n.method="throw",n.arg=l.arg)}}}function _(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,_(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),f;var o=d(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,f;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,f):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,f)}function A(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function k(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function L(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(A,this),this.reset(!0)}function S(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:O}}function O(){return{value:void 0,done:!0}}return p.prototype=m,o(b,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:p,configurable:!0}),p.displayName=s(m,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,m):(e.__proto__=m,s(e,l,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},w(E.prototype),s(E.prototype,c,(function(){return this})),e.AsyncIterator=E,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new E(u(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},w(b),s(b,l,"Generator"),s(b,a,(function(){return this})),s(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=S,L.prototype={constructor:L,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(k),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),f},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),k(n),f}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;k(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:S(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),f}},e}const Ot=function(){var e=(0,a.wA)(),t=(0,a.d4)((function(e){return e.motd.messages})),n=(0,a.d4)((function(e){return e.motd.isExpanded})),r=(0,a.d4)((function(e){return e.global.authToken})),f=(0,a.d4)((function(e){return e.global.env})),h=(0,a.d4)((function(e){return e.motd.isFirstView})),p=(0,a.d4)((function(e){return e.motd.selectedMessage})),m=(0,a.d4)((function(e){return e.global.language})),g=(0,u.lh)("game_id");return h||((0,u.ge)("isFirstView","1"),e(l.Gc.setIsFirstView(!0))),(0,i.useEffect)((function(){var t=function(){var t=(0,o.A)(St().mark((function t(){var n,r,o,i;return St().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e((0,l.TC)());case 3:if(n=t.sent,0!==(r=n.payload).messages.length){t.next=11;break}(0,u.wi)("No messages were returned"),(0,u.eA)(!1),(0,u.Ln)({type:"api_load"}),t.next=19;break;case 11:return t.next=13,(0,d.LE)(null===(o=r.messages[0])||void 0===o||null===(i=o.content)||void 0===i?void 0:i.popupImage);case 13:(0,u.eA)(!0),(0,u.Ln)({type:"ready"}),r.messages.forEach((function(e){u.zf.push({messageId:e.messageId}),console.log("INTERACTION STATUS ==========",u.zf)})),(0,u.Ek)(0),(0,u.rK)();case 19:t.next=25;break;case 21:t.prev=21,t.t0=t.catch(0),(0,u.wi)("API request failed",t.t0),(0,u.eA)(!1);case 25:case"end":return t.stop()}}),t,null,[[0,21]])})));return function(){return t.apply(this,arguments)}}();(0,u.Ln)({type:"start"}),t(),window.Telescope_CB_NeedFullScreenMotd=function(){return h},window.Telescope_CB_Reset=function(){e(l.Gc.isExpanded(!1))},window.Telescope_CB_LastInputDeviceUpdated=function(t,n){e(s.f.setLastInputDevice(t)),e(s.f.setLastInputGamepad(n))},window.Telescope_CB_GainFocus=function(t,n,r){e(s.f.setIsFocused(!0))},window.Telescope_CB_LoseFocus=function(){e(s.f.setIsFocused(!1))},window.Telescope_CB_SoundVolumeUpdated=function(e){Lt.B.emit("volumeUpdated",e)},window.Telescope_CB_RequestFullScreenMotd=function(){(0,u.wi)("requested fullscreen"),e(l.Gc.setNeedFullScreen(!0))}}),[]),(0,i.useEffect)((function(){window.Telescope_CB_SwitchSleep=function(e){return 1===e?(Lt.B.emit("sleep"),(0,u.n)(p),(0,u.rK)(p)):0===e&&(Lt.B.emit("wake"),(0,u.Ek)(p)),!0}}),[p]),(0,i.useEffect)((function(){e(s.f.setLastInputDevice((0,u.lh)("last_input_device",!0))),e(s.f.setLastInputGamepad((0,u.lh)("last_input_gamepad",!0)))}),[(0,u.lh)("last_input_device",!0),(0,u.lh)("last_input_gamepad",!0)]),"dev"!==f&&"shaqa"!==f||r?t&&"cert"!==f?i.createElement(c.A,{themeName:g},i.createElement(E,{language:m}),!n&&i.createElement(et,{messages:t}),n&&i.createElement(kt,null)):i.createElement("div",null,"No Messages"):i.createElement("div",null)}},4212:(e,t,n)=>{e.exports=n.p+"31d6cfe0d16ae931b73c.ttf"}}]);