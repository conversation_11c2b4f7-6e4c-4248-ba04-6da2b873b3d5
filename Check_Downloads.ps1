# Check download status and guide user

Write-Host "=== NVIDIA DRIVER DOWNLOAD STATUS ===" -ForegroundColor Green
Write-Host ""

# Check BITS transfer
$bitsJobs = Get-BitsTransfer -ErrorAction SilentlyContinue
if ($bitsJobs) {
    foreach ($job in $bitsJobs) {
        if ($job.BytesTotal -gt 0) {
            $percentComplete = [math]::Round(($job.BytesTransferred / $job.BytesTotal) * 100, 2)
            $mbTransferred = [math]::Round($job.BytesTransferred / 1MB, 2)
            $mbTotal = [math]::Round($job.BytesTotal / 1MB, 2)
            
            Write-Host "NVIDIA Driver 566.03 Download:" -ForegroundColor Yellow
            Write-Host "Progress: $percentComplete% ($mbTransferred MB / $mbTotal MB)" -ForegroundColor White
            Write-Host "Status: $($job.JobState)" -ForegroundColor White
        }
    }
} else {
    Write-Host "No active downloads found." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== DOWNLOADED FILES ===" -ForegroundColor Green

# Check for downloaded files
if (Test-Path "nvidia_566.03.exe") {
    $size = [math]::Round((Get-Item "nvidia_566.03.exe").Length / 1MB, 2)
    Write-Host "✓ NVIDIA Driver 566.03: $size MB" -ForegroundColor Green
} else {
    Write-Host "✗ NVIDIA Driver 566.03: Not found" -ForegroundColor Red
}

if (Test-Path "DDU.exe") {
    Write-Host "✓ DDU: Ready" -ForegroundColor Green
} else {
    Write-Host "✗ DDU: Need to download manually" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== NEXT STEPS ===" -ForegroundColor Yellow
Write-Host "1. Wait for NVIDIA driver download to complete" -ForegroundColor White
Write-Host "2. Download DDU from the browser page (guru3d.com)" -ForegroundColor White
Write-Host "3. Extract DDU to a folder" -ForegroundColor White
Write-Host "4. Boot to Safe Mode and run DDU" -ForegroundColor White
Write-Host "5. Install NVIDIA Driver 566.03" -ForegroundColor White
Write-Host "6. Test Call of Duty - weapons should appear!" -ForegroundColor White

Write-Host ""
Write-Host "Run this script again to check progress!" -ForegroundColor Cyan
