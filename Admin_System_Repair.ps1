# CALL OF DUTY WEAPON FIX - ADMIN SYSTEM REPAIR SCRIPT
# Run this script as Administrator

Write-Host "=== CALL OF DUTY WEAPON RENDERING FIX - SYSTEM REPAIR ===" -ForegroundColor Green
Write-Host "This script will repair Windows system files and DirectX components" -ForegroundColor Yellow
Write-Host ""

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Red
    pause
    exit 1
}

Write-Host "Step 1: Running System File Checker (SFC)..." -ForegroundColor Cyan
Write-Host "This may take 10-15 minutes..." -ForegroundColor Yellow
sfc /scannow

Write-Host ""
Write-Host "Step 2: Running DISM Image Health Check..." -ForegroundColor Cyan
DISM /Online /Cleanup-Image /CheckHealth

Write-Host ""
Write-Host "Step 3: Running DISM Image Restore Health..." -ForegroundColor Cyan
Write-Host "This may take 15-30 minutes..." -ForegroundColor Yellow
DISM /Online /Cleanup-Image /RestoreHealth

Write-Host ""
Write-Host "Step 4: Clearing additional graphics caches..." -ForegroundColor Cyan

# Clear NVIDIA shader cache
$nvidiaCache = "$env:LOCALAPPDATA\NVIDIA\DXCache"
if (Test-Path $nvidiaCache) {
    Remove-Item -Path $nvidiaCache -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "Cleared NVIDIA DX Cache" -ForegroundColor Green
}

# Clear DirectX shader cache
$dxCache = "$env:LOCALAPPDATA\D3DSCache"
if (Test-Path $dxCache) {
    Remove-Item -Path $dxCache -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "Cleared DirectX Shader Cache" -ForegroundColor Green
}

# Clear Windows graphics cache
$winCache = "$env:LOCALAPPDATA\Microsoft\Windows\INetCache"
if (Test-Path $winCache) {
    Remove-Item -Path "$winCache\*" -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "Cleared Windows Graphics Cache" -ForegroundColor Green
}

Write-Host ""
Write-Host "=== SYSTEM REPAIR COMPLETED ===" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Restart your computer" -ForegroundColor White
Write-Host "2. Verify Call of Duty files in Xbox App" -ForegroundColor White
Write-Host "3. Configure NVIDIA Control Panel settings" -ForegroundColor White
Write-Host "4. Test the game" -ForegroundColor White
Write-Host ""
Write-Host "If weapons still don't render, proceed with NVIDIA driver rollback to 566.03" -ForegroundColor Yellow

pause
