2025/10/01 02:50:47 ===LOG START 2025-10-01 02:50:47.2432905 -0500 CDT m=+0.002533701===
2025/10/01 02:50:47 Checking if the driver is in the game folder
2025/10/01 02:50:47 C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe Get-Service -Name atvi-randgrid_msstore -ErrorAction SilentlyContinue
2025/10/01 02:50:47 
Status   Name               DisplayName                           
------   ----               -----------                           
Stopped  atvi-randgrid_m... atvi-randgrid_msstore                 



2025/10/01 02:50:47 Service atvi-randgrid_msstore already exists, checking service status...
2025/10/01 02:50:47 C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe (Get-Service -Name atvi-randgrid_msstore -ErrorAction SilentlyContinue).Status
2025/10/01 02:50:47 Stopped
2025/10/01 02:50:47 Updating service atvi-randgrid_msstore to the driver C:\XboxGames\Call of Duty\Content\Randgrid.sys
2025/10/01 02:50:47 C:\WINDOWS\system32\sc.exe config atvi-randgrid_msstore binPath=C:\XboxGames\Call of Duty\Content\Randgrid.sys
2025/10/01 02:50:47 [SC] ChangeServiceConfig SUCCESS

2025/10/01 02:50:47 Waiting for service to update...
2025/10/01 02:50:48 C:\WINDOWS\system32\sc.exe qc atvi-randgrid_msstore
2025/10/01 02:50:48 [SC] QueryServiceConfig SUCCESS

SERVICE_NAME: atvi-randgrid_msstore
        TYPE               : 1  KERNEL_DRIVER 
        START_TYPE         : 3   DEMAND_START
        ERROR_CONTROL      : 1   NORMAL
        BINARY_PATH_NAME   : \??\C:\XboxGames\Call of Duty\Content\Randgrid.sys
        LOAD_ORDER_GROUP   : 
        TAG                : 0
        DISPLAY_NAME       : atvi-randgrid_msstore
        DEPENDENCIES       : 
        SERVICE_START_NAME : 

2025/10/01 02:50:48 Service updated successfully.
2025/10/01 02:50:48 Driver is ready.
2025/10/01 02:50:48 ===LOG END===
