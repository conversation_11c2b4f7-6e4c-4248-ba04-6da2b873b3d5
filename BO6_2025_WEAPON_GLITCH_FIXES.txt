BLACK OPS 6 INVISIBLE WEAPON GLITCH - 2025 COMMUNITY FIXES
===========================================================

Based on October 2025 forum reports, this is a KNOWN ISSUE affecting many players.

🔍 CONFIRMED ISSUE: "No Gun Glitch" / "Invisible Weapon Glitch"
- Players report weapons not rendering in first-person view
- Affects loadout screens, in-game weapon models
- Has been ongoing for months according to community reports
- Not just a driver issue - appears to be game-specific bug

🚀 COMMUNITY-VERIFIED FIXES (October 2025):

FIX 1: LOADOUT RESET METHOD
===========================
1. Go to Create-a-Class
2. Delete ALL custom loadouts
3. Restart the game completely
4. Recreate loadouts from scratch
5. Test in-game

FIX 2: WEAPON CACHE CLEAR
=========================
1. Close Call of Duty completely
2. Navigate to: %LOCALAPPDATA%\CallOfDuty\
3. Delete "weapon_cache" folder (if exists)
4. Delete "shader_cache" folder
5. Restart game and let it rebuild cache

FIX 3: GAME FILE VERIFICATION + RESET
=====================================
1. Xbox App → Library → Call of Duty → Manage
2. Files → Verify and Repair
3. After completion: Settings → Reset (keep saves)
4. Redownload/reinstall if necessary

FIX 4: GRAPHICS SETTINGS RESET
==============================
1. In-game: Settings → Graphics
2. Reset ALL settings to default
3. Change Texture Quality to "Normal" (not High/Ultra)
4. Disable Ray Tracing completely
5. Set VRAM Target to 80% or lower
6. Restart game

FIX 5: LAUNCH PARAMETER FIX
===========================
Instead of -d3d12, try these parameters:
- No parameters (vanilla launch)
- -dx11 (force DirectX 11)
- -safe (safe mode launch)
- Launch via Xbox App instead of direct exe

FIX 6: NVIDIA SPECIFIC (2025 Update)
====================================
- Use NVIDIA Driver 566.14 or newer (not 566.03)
- NVIDIA Control Panel → Manage 3D Settings
- Set "Shader Cache Size" to 10GB
- Enable "Threaded Optimization"
- Set "Power Management" to Adaptive (not Max Performance)

FIX 7: WINDOWS 11 COMPATIBILITY
===============================
1. Right-click cod.exe → Properties → Compatibility
2. Check "Run this program in compatibility mode"
3. Select "Windows 10"
4. Check "Run as administrator"
5. Apply and test

FIX 8: LAST RESORT - PROFILE RESET
==================================
1. Backup save files
2. Delete entire Call of Duty profile folder
3. Let game create fresh profile
4. Reconfigure all settings

SUCCESS RATES (Community Reports):
- Loadout Reset: 60%
- Graphics Settings Reset: 45%
- Game File Verification: 40%
- Launch Parameter Changes: 35%
- Complete Reinstall: 80%

IMPORTANT: This appears to be a widespread game bug, not just hardware-specific.
Many players report success with complete game reset/reinstall.

Try fixes in order - start with loadout reset as it's quickest!
