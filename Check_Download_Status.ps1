# CALL OF DUTY WEAPON FIX - DOWNLOAD STATUS CHECKER

Write-Host "=== CALL OF DUTY WEAPON FIX - STATUS CHECK ===" -ForegroundColor Green
Write-Host ""

# Check BITS Transfer status
Write-Host "Checking NVIDIA Driver Download Status..." -ForegroundColor Cyan
$bitsJobs = Get-BitsTransfer -ErrorAction SilentlyContinue
if ($bitsJobs) {
    foreach ($job in $bitsJobs) {
        $percentComplete = [math]::Round(($job.BytesTransferred / $job.BytesTotal) * 100, 2)
        $mbTransferred = [math]::Round($job.BytesTransferred / 1MB, 2)
        $mbTotal = [math]::Round($job.BytesTotal / 1MB, 2)
        
        Write-Host "Job: $($job.DisplayName)" -ForegroundColor Yellow
        Write-Host "Status: $($job.JobState)" -ForegroundColor White
        Write-Host "Progress: $percentComplete% ($mbTransferred MB / $mbTotal MB)" -ForegroundColor White
        Write-Host ""
    }
} else {
    Write-Host "No active BITS transfers found." -ForegroundColor Yellow
}

# Check if files exist
Write-Host "Checking Downloaded Files..." -ForegroundColor Cyan
$files = @(
    "nvidia_566.03.exe",
    "dxwebsetup.exe", 
    "vc_redist.x64.exe",
    "DDU.zip"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        $size = [math]::Round((Get-Item $file).Length / 1MB, 2)
        Write-Host "OK $file ($size MB)" -ForegroundColor Green
    } else {
        Write-Host "MISSING $file (Not found)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== NEXT STEPS ===" -ForegroundColor Yellow
Write-Host "1. Wait for NVIDIA driver download to complete" -ForegroundColor White
Write-Host "2. Download DDU from the opened browser page" -ForegroundColor White
Write-Host "3. Run Admin_System_Repair.ps1 as Administrator" -ForegroundColor White
Write-Host "4. Verify game files in Xbox App" -ForegroundColor White
Write-Host "5. Configure NVIDIA Control Panel settings" -ForegroundColor White
Write-Host ""
Write-Host "Run this script again to check download progress!" -ForegroundColor Cyan
