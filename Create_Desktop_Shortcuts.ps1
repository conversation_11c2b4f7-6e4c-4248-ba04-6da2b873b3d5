# Create desktop shortcuts for easy access after driver install

$desktop = [Environment]::GetFolderPath("Desktop")

# Create shortcut to NVIDIA driver
$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("$desktop\Install NVIDIA 566.03.lnk")
$Shortcut.TargetPath = "C:\XboxGames\Call of Duty_1\nvidia_566.03.exe"
$Shortcut.WorkingDirectory = "C:\XboxGames\Call of Duty_1"
$Shortcut.Description = "NVIDIA Driver 566.03 for COD Fix"
$Shortcut.Save()

# Create shortcut to game launcher
$Shortcut2 = $WshShell.CreateShortcut("$desktop\Launch COD Fixed.lnk")
$Shortcut2.TargetPath = "C:\XboxGames\Call of Duty_1\Launch_COD_Fixed.bat"
$Shortcut2.WorkingDirectory = "C:\XboxGames\Call of Duty_1"
$Shortcut2.Description = "Launch Call of Duty with Fix Parameters"
$Shortcut2.Save()

# Create shortcut to instructions
$Shortcut3 = $WshShell.CreateShortcut("$desktop\Safe Mode Instructions.lnk")
$Shortcut3.TargetPath = "C:\XboxGames\Call of Duty_1\SAFE_MODE_INSTRUCTIONS.txt"
$Shortcut3.WorkingDirectory = "C:\XboxGames\Call of Duty_1"
$Shortcut3.Description = "Nuclear Driver Fix Instructions"
$Shortcut3.Save()

Write-Host "Desktop shortcuts created!" -ForegroundColor Green
Write-Host "- Install NVIDIA 566.03.lnk" -ForegroundColor Yellow
Write-Host "- Launch COD Fixed.lnk" -ForegroundColor Yellow  
Write-Host "- Safe Mode Instructions.lnk" -ForegroundColor Yellow
