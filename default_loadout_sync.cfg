// Call of Duty Black Ops 6 - Default Loadout Sync Configuration
// This forces proper weapon synchronization between Firing Range and Multiplayer

// Loadout Synchronization Settings
seta cg_loadoutSync "1"
seta cg_weaponSync "1" 
seta cg_forceLoadoutRefresh "1"

// Weapon Rendering - Force Enable
seta cg_drawGun "1"
seta cg_drawWeapon "1"
seta r_drawWeapons "1"
seta r_firstPersonWeapon "1"

// Multiplayer Weapon Sync
seta mp_weaponSync "1"
seta mp_loadoutValidation "1"
seta mp_forceWeaponRefresh "1"

// Network Sync Settings
seta cl_loadoutSync "1"
seta cl_weaponCacheSync "1"
seta net_loadoutRefresh "1"

// Cache Settings - Force Refresh
seta r_preloadWeapons "1"
seta r_weaponCacheSize "256"
seta r_loadoutCacheRefresh "1"

// Firing Range vs Multiplayer Sync
seta firingrange_syncToMP "1"
seta mp_syncFromFiringRange "1"

// Debug Settings for Weapon Sync
seta developer_weaponSync "0"
seta logfile_weaponSync "0"

// Force weapon model loading in all modes
seta cg_weaponBob "1"
seta cg_weaponCycleDelay "0"
seta cg_weaponOffsetX "0"
seta cg_weaponOffsetY "0"
seta cg_weaponOffsetZ "0"

// Ensure weapon attachments sync
seta cg_weaponAttachments "1"
seta mp_weaponAttachmentSync "1"

// End of loadout sync configuration
